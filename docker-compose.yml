services:
  postgres:
    image: postgres:15
    container_name: fantasy-maps-postgres
    environment:
      POSTGRES_DB: fantasy-maps
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - fantasy-maps-network

  redis:
    image: redis:7-alpine
    container_name: fantasy-maps-redis
    ports:
      - "6379:6379"
    networks:
      - fantasy-maps-network

  backend:
    build: .
    container_name: fantasy-maps-backend
    environment:
      - DATABASE_PASSWORD=postgres123
      - DATABASE_USERNAME=postgres
      - KEYSTORE_PASSWORD=fmaps
      - KEYSTORE_ALIAS=localhost
      - SPRING_DATASOURCE_URL=********************************************
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    ports:
      - "8443:8443"
    depends_on:
      - postgres
      - redis
    networks:
      - fantasy-maps-network

volumes:
  postgres_data:

networks:
  fantasy-maps-network:
    driver: bridge
