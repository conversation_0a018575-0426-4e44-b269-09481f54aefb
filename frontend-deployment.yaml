apiVersion: apps/v1
kind: Deployment
metadata:
  name: angular-frontend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: angular-frontend
  template:
    metadata:
      labels:
        app: angular-frontend
    spec:
      containers:
      - name: frontend
        image: angular-frontend:local
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 80
---
apiVersion: v1
kind: Service
metadata:
  name: angular-frontend
spec:
  selector:
    app: angular-frontend
  ports:
    - port: 80
      targetPort: 80
