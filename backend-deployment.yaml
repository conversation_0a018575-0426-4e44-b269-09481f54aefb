apiVersion: apps/v1
kind: Deployment
metadata:
  name: spring-backend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: spring-backend
  template:
    metadata:
      labels:
        app: spring-backend
    spec:
      containers:
      - name: backend
        image: fantasy-maps-backend:latest
        imagePullPolicy: IfNotPresent
        env:
        - name: DATABASE_PASSWORD
          value: "postgres123"
        - name: DATABASE_USERNAME
          value: "postgres"
        - name: KEYSTORE_PASSWORD
          value: "fmaps"
        - name: KEYSTORE_ALIAS
          value: "localhost"
        - name: SPRING_DATASOURCE_URL
          value: "********************************************"
        - name: REDIS_HOST
          value: "redis"
        - name: REDIS_PORT
          value: "6379"
        ports:
        - containerPort: 8443
---
apiVersion: v1
kind: Service
metadata:
  name: spring-backend
spec:
  selector:
    app: spring-backend
  ports:
    - port: 8443
      targetPort: 8443