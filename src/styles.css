@import "normalize.css";

@font-face {
  font-family: 'Nova Cut';
  src: url('./assets/fonts/NovaCut.ttf') format('truetype');
}

@font-face {
  font-family: 'Nova Slim';
  src: url('./assets/fonts/NovaSlim.ttf') format('truetype');
}

@font-face {
  font-family: 'Istok Web';
  src: url('./assets/fonts/IstokWeb.ttf') format('truetype');
}

:root {
  --background-color: #f6f1ee;
  --background-color-deemed: #D9D9D9;
  --background-color-dark: #373A40;
  --accent-color: #CEA473;
  --accent-color-dark: #686D76;
  --white: #ffffff;
  --active-red: #FA4751;
  --active-green: #5FE668;
  --favorite-yellow: #ead01e;

  --components-vertical-padding: 0.4rem;
  --components-horizontal-padding: 1rem;

  --text-shadow: black 0 0 2px, black 1px 1px 1px;

  --navbar-height: 5rem;
  --options-bar-height: 2.5rem;
}

html{
  background-color: var(--background-color-dark);
}

html, body {
  min-height: 100vh;
}

app-root {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

body {
  font-family: 'Roboto', sans-serif;
  background-color: var(--background-color);
  color: var(--background-color-dark);
}

button {
  margin: 0;
  padding: 0;
  border: none;
  background: none;
  cursor: pointer;
}

h1 {
  font-family: 'Nova Cut', sans-serif;
  font-weight: normal;
  margin: 0;
}

h2, h3, h4, h5, h6 {
  font-family: 'Nova Slim', sans-serif;
  font-weight: normal;
  margin: 0;
}

h1 {
  font-size: 2.4rem;
}

h2 {
  font-size: 1.4rem;
}

h3 {
  font-size: 1.3rem;
}

h3 {
  font-size: 1rem;
}

h4 {
  font-size: 0.8rem;
}

ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

a {
  text-decoration: none;
  color: inherit;
}


body {
  margin: 0;
  font-family: Roboto, "Helvetica Neue", sans-serif;
}

.custom-backdrop {
  background-color: rgba(0, 0, 0, 0.5);
}

.mat-mdc-dialog-container {
  --mdc-dialog-container-shape: 16px;
}

::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: var(--background-color-dark);
}

::-webkit-scrollbar-thumb {
  background: var(--accent-color-dark);
  border-radius: 6px;
  border: 2px solid var(--background-color-dark);
}

body {
  ::-webkit-scrollbar-track {
    border-radius: 6px;
  }
}

/* remove border for mat-form-field */
.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,
.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,
.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing {
  border: none !important;
}

/* autocomplete panel */
.mat-mdc-autocomplete-panel{
  background-color: var(--background-color) !important;
  border-radius: 20px !important;
  border: 1px solid var(--accent-color-dark) !important;
  padding: 0 !important;
}
