package org.fantasymaps.backend.dtos;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.fantasymaps.backend.config.AppConfig;

import java.io.Serializable;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class UserDto implements Serializable {
    int id;
    @Pattern(message = AppConfig.nameMismatchMessage,
            regexp = AppConfig.namePattern)
    @NotBlank(message = "Username is mandatory")
    String username;
    Role role;
    String token;
}
