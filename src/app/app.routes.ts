import {Routes} from '@angular/router';
import {MapsGalleryMasonryComponent} from "./Components/Galleries/maps-gallery-masonry/maps-gallery-masonry.component";
import {UploadMapPopUpFormComponent} from "./Components/Forms/pop-up-form/upload-map-pop-up-form/upload-map-pop-up-form.component";
import {AuthenticationComponent} from "./Components/authentication/authentication.component";
import {LoginGuard} from "./Config/login.guard";
import {MapViewComponent} from "./Components/map-view/map-view.component";
import {ManageContentComponent} from "./Components/Menu/manage-content/manage-content.component";
import {Role} from "./Dtos/role";
import {AuthGuard} from "./Config/auth.guard";
import {PopUpFormGuard} from "./Config/pop-up-form.guard";
import {
  CreateBundlePopUpFormComponent
} from "./Components/Forms/pop-up-form/create-bundle-pop-up-form/create-bundle-pop-up-form.component";
import {ManageBundlesComponent} from "./Components/Galleries/manage-bundles/manage-bundles.component";
import {
  FavoriteMapsGalleryMasonryComponent
} from "./Components/Galleries/maps-gallery-masonry/favorite-maps-gallery-masonry/favorite-maps-gallery-masonry.component";
import {ManageCreatorMapsComponent} from "./Components/Galleries/manage-maps/manage-creator-maps/manage-creator-maps.component";

export const routes: Routes = [
  {path: 'maps', component: MapsGalleryMasonryComponent},
  {path: 'map/:id', component: MapViewComponent},
  {path: 'saved-maps', component: FavoriteMapsGalleryMasonryComponent},
  {
    path: 'content', component: ManageContentComponent,
    canActivate: [AuthGuard], data: {roles: [Role.CREATOR, Role.CUSTOMER]},
    children: [
      {
        path: 'maps',
        component: ManageCreatorMapsComponent,
        children: [
          {
            path: 'upload', canActivate: [PopUpFormGuard], data: { dialogComponent: UploadMapPopUpFormComponent, width: '70vw'},
            component: UploadMapPopUpFormComponent }
        ]
      },
      {
        path: 'bundles',
        component: ManageBundlesComponent,
        children: [
          {
            path: 'create', canActivate: [PopUpFormGuard], data: { dialogComponent: CreateBundlePopUpFormComponent, width: '30vw'},
            component: CreateBundlePopUpFormComponent
          }
        ]
      },
      ]
  },
  {path: 'auth', component: AuthenticationComponent,
    canActivate: [LoginGuard]},
  {path: '**', redirectTo: 'maps'}
];
