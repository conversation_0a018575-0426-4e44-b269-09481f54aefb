import {Routes} from '@angular/router';
import {MapsGalleryMasonryComponent} from "./Galleries/maps-gallery-masonry/maps-gallery-masonry.component";
import {UploadMapPopUpFormComponent} from "./Forms/pop-up-form/upload-map-pop-up-form/upload-map-pop-up-form.component";
import {AuthenticationComponent} from "./authentication/authentication.component";
import {LoginGuard} from "./config/login.guard";
import {ActivateGuard} from "./config/activate.guard";
import {MapViewComponent} from "./map-view/map-view.component";
import {ManageContentComponent} from "./manage layout/manage-content/manage-content.component";
import {Role} from "./dtos/role";
import {AuthGuard} from "./config/auth.guard";
import {PopUpFormGuard} from "./config/pop-up-form.guard";
import {
  CreateBundlePopUpFormComponent
} from "./Forms/pop-up-form/create-bundle-pop-up-form/create-bundle-pop-up-form.component";
import {ManageBundlesComponent} from "./Galleries/manage-bundles/manage-bundles.component";
import {
  FavoriteMapsGalleryMasonryComponent
} from "./Galleries/maps-gallery-masonry/favorite-maps-gallery-masonry/favorite-maps-gallery-masonry.component";
import {ManageCreatorMapsComponent} from "./Galleries/manage-maps/manage-creator-maps/manage-creator-maps.component";

export const routes: Routes = [
  {path: 'maps', component: MapsGalleryMasonryComponent},
  {path: 'map/:id', component: MapViewComponent},
  {path: 'saved-maps', component: FavoriteMapsGalleryMasonryComponent},
  {
    path: 'content', component: ManageContentComponent,
    canActivate: [AuthGuard], data: {roles: [Role.CREATOR, Role.CUSTOMER]},
    children: [
      {
        path: 'maps',
        component: ManageCreatorMapsComponent,
        children: [
          {
            path: 'upload', canActivate: [PopUpFormGuard], data: { dialogComponent: UploadMapPopUpFormComponent, width: '70vw'},
            component: UploadMapPopUpFormComponent }
        ]
      },
      {
        path: 'bundles',
        component: ManageBundlesComponent,
        children: [
          {
            path: 'create', canActivate: [PopUpFormGuard], data: { dialogComponent: CreateBundlePopUpFormComponent, width: '30vw'},
            component: CreateBundlePopUpFormComponent
          }
        ]
      },
      {
        path: 'subscriptions',
        loadComponent: () =>
          import('./Galleries/manage-subscriptions/manage-subscriptions.component').then((m) => m.ManageSubscriptionsComponent),
        children: [
          // {path: 'create', component:  }
        ]
      }
      ]
  },
  {path: 'auth', component: AuthenticationComponent, canActivate: [LoginGuard]},
  {path: 'activate', component: AuthenticationComponent, canActivate: [ActivateGuard]},
  {path: '**', redirectTo: 'maps'}
];
