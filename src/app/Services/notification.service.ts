import {Injectable} from '@angular/core';
import {Subject} from "rxjs";

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  errorMessages: Subject<string> = new Subject<string>();
  infoMessages: Subject<string> = new Subject<string>();
  successMessages: Subject<string> = new Subject<string>();

  constructor() {
  }

  showError(message: string): void {
    this.errorMessages.next(message);
  }

  showInfo(message: string): void {
    this.infoMessages.next(message);
  }

  showSuccess(message: string): void {
    this.successMessages.next(message);
  }
}
