import {Injectable} from '@angular/core';
import {HttpErrorResponse} from "@angular/common/http";
import {NotificationService} from "./notification.service";
import {AuthService} from "./auth.service";
import {Router} from "@angular/router";


@Injectable({
  providedIn: 'root'
})
export class GlobalErrorService {
  constructor(private errorHandler: NotificationService, private authService: AuthService, private router: Router) {
  }

  handleError(error: HttpErrorResponse): void {
    if (!error) {
      this.errorHandler.showError("Unknown error occurred");
      return;
    }

    if (error.status == 403) {
      if (this.authService.currentUserValue?.role)
        this.authService.logout(false);
      this.errorHandler.showError("You must login to perform this action");
      this.router.navigate(['/auth']);
      return;
    }

    this.errorHandler.showError(`Error occurred`);
    console.error(error);
  }
}
