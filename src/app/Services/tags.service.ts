import {Injectable} from "@angular/core";
import {baseUrl} from "../app.config";
import {HttpClient} from "@angular/common/http";
import {TagDto} from "../Dtos/tag.dto";
import {Observable} from "rxjs";

@Injectable({
  providedIn: 'root'
})
export class TagService {
  constructor(private http: HttpClient) {
  }

  getTags(): Observable<TagDto[]> {
    return this.http.get<TagDto[]>(`${baseUrl}/tags`);
  }
}
