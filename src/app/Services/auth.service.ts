import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {BehaviorSubject, Observable} from 'rxjs';
import {map} from 'rxjs/operators';
import {UserDto} from "../Dtos/user.dto";
import {baseUrl} from "../app.config";
import {AuthRequestDto} from "../Dtos/auth-request.dto";
import {NotificationService} from "./notification.service";
import {RegisterRequestDto} from "../Dtos/register-request.dto";
import {Role} from "../Dtos/role";
import {Router} from "@angular/router";

@Injectable({providedIn: 'root'})
export class AuthService {
  public currentUser: Observable<UserDto | null>;
  private currentUserSubject: BehaviorSubject<UserDto | null>;

  constructor(private http: HttpClient, private notificationService: NotificationService, private router: Router) {
    this.currentUserSubject = new BehaviorSubject<any>(JSON.parse(localStorage.getItem('currentUser') || '{}'));
    this.currentUser = this.currentUserSubject.asObservable();
  }

  public get currentUserValue() {
    return this.currentUserSubject.value;
  }

  login(username: string, password: string): Observable<UserDto> {
    let authRequestDto = new AuthRequestDto(username, password);
    return this.http.post<any>(baseUrl + '/user/authenticate', authRequestDto)
      .pipe(map(user => {
        if (user && user.token) {
          localStorage.setItem('currentUser', JSON.stringify(user));
          this.currentUserSubject.next(user);
        }
        return user;
      }));
  }

  logout(notify: boolean = true) {
    return this.http.post<any>(baseUrl + '/logout', {}).subscribe(
      {
        next: () => {
          localStorage.removeItem('currentUser');
          this.currentUserSubject.next(null);
          if (notify)
            this.notificationService.showInfo('Logged out successfully');
          window.location.href = '/';
        },
        error: (error) => {
          this.notificationService.showError(error.error.message);
        }
      }
    );
  }

  register(username: string, password: string, role: Role): Observable<UserDto> {
    return this.http.post<any>(baseUrl + '/user/register', new RegisterRequestDto(username, password, role));
  }

  isCreator() {
    return this.currentUserValue?.role === Role.CREATOR;
  }

  updateCurrentUser() {
    this.http.get<UserDto>(baseUrl + '/user').subscribe(
      {
        next: (user) => {
          localStorage.setItem('currentUser', JSON.stringify(user));
          this.currentUserSubject.next(user);
        },
        error: (error) => {
          this.logout(false);
        }
      }
    );
  }
}
