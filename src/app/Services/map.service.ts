import {Injectable} from '@angular/core';
import {HttpClient, HttpParams} from "@angular/common/http";
import {baseUrl} from "../app.config";
import {MapDto} from "../Dtos/map.dto";
import {Observable, switchMap} from "rxjs";
import {getDownloadURL, ref, Storage} from "@angular/fire/storage";
import {MapDetailsDto} from "../Dtos/map-details.dto";
import {ManageMapItemDto} from "../Dtos/manage-map-item.dto";
import {IsMapImage} from "../Dtos/is-map-image";
import {MapSizeDto} from "../Dtos/map-size.dto";
import {TagDto} from "../Dtos/tag.dto";

@Injectable({
  providedIn: 'root'
})
export class MapService {

  constructor(private http: HttpClient, private storage: Storage) {
  }

  uploadMap(
    file: File,
    mapName: string,
    description: string | null,
    mapSize: MapSizeDto | null,
    tags: TagDto[]
  ): Observable<MapDto> {
    const formData = new FormData();

    formData.append('file', file);

    const mapData = {
      name: mapName,
      description: description,
      size: mapSize,
      tags: tags
    };
    formData.append('map', new Blob([JSON.stringify(mapData)], { type: 'application/json' }));

    return this.http.post<MapDto>(`${baseUrl}/map`, formData);
  }


  getPreviewMaps(page: number, filterTags: TagDto[]): Observable<MapDto[]> {
    let params = new HttpParams().set("tags", JSON.stringify(filterTags));

    return <Observable<MapDto[]>> this.http.get<MapDto[]>(`${baseUrl}/maps?page=${page}`, { params })
      .pipe(switchMap(maps => this.mapImagePreviewUrls(maps)));
  }


  getFavoritePreviewMaps(page: number, filterTags: TagDto[]): Observable<MapDto[]> {
    let params = new HttpParams().set("tags", JSON.stringify(filterTags));
    return <Observable<MapDto[]>> this.http.get<MapDto[]>(`${(baseUrl)}/maps/favorite?page=${page}`, { params })
      .pipe(switchMap((maps) => this.mapImagePreviewUrls(maps)));
  }

  toggleFavorite(id: number, isFavorite: boolean) {
    return this.http.post(`${baseUrl}/map/${id}/favorite`, {favorite: isFavorite});
  }

  getMapDetails(id: number):Observable<MapDetailsDto>{
    return <Observable<MapDetailsDto>> this.http.get<MapDetailsDto>(`${baseUrl}/map/${id}`)
      .pipe(switchMap((map) => this.mapImageOriginalUrl(map)));
  }

  getPreviewMapsByCreator(creatorId: number, page: number): Observable<ManageMapItemDto[]> {
    return <Observable<ManageMapItemDto[]>> this.http.get<ManageMapItemDto[]>(`${baseUrl}/maps/manage/creator/${creatorId}?page=${page}`)
      .pipe(switchMap((maps) => this.mapImagePreviewUrls(maps)));
  }

  /**
   * Get maps that are bundled with the map with the given id
   * @param mapId
   * @param page
   */
  getPreviewBundledMaps(mapId: number, page: number) {
    return <Observable<ManageMapItemDto[]>> this.http.get<ManageMapItemDto[]>(`${baseUrl}/maps/bundled/${mapId}?page=${page}`)
      .pipe(switchMap((maps) => this.mapImagePreviewUrls(maps)));
  }

  deleteMap(mapId: number) {
    return this.http.delete(`${baseUrl}/map/${mapId}`);
  }

  getMapPreview(mapUrl: string) {
    return getDownloadURL(ref(this.storage, `preview/${mapUrl}`));
  }

  getMapOriginal(mapUrl: string) {
    return getDownloadURL(ref(this.storage, `original/${mapUrl}`));
  }

  private async mapImagePreviewUrl(map: IsMapImage) {
    this.getMapPreview(map.url).then((url) => map.url = `${url}?cb=${new Date().getTime()}`);
    // TODO remove cache buster
    return map;
  }

  private async mapImagePreviewUrls(maps: IsMapImage[]) {
    const mapPromises = maps.map((map) => this.mapImagePreviewUrl(map));
    await Promise.all(mapPromises);
    return maps;
  }

  private async mapImageOriginalUrl(map: IsMapImage) {
    this.getMapOriginal(map.url).then((url) => map.url = `${url}`);
    return map;
  }
}
