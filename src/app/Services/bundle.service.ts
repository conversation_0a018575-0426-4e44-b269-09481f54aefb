import {Injectable} from "@angular/core";
import {baseUrl} from "../app.config";
import {ManageBundleItemDto} from "../Dtos/manage-bundle-item.dto";
import {HttpClient} from "@angular/common/http";
import {CreateBundleDto} from "../Dtos/create-bundle.dto";

@Injectable({
  providedIn: 'root'
})
export class BundleService {
  constructor(private http: HttpClient) {
  }

  getBundlesByCreator(creatorId: number, page: number) {
    return this.http.get<ManageBundleItemDto[]>(`${baseUrl}/bundles/manage/creator/${creatorId}?page=${page}`);
  }

  createBundle(name: string | null, maps: number[]) {
    return this.http.post(`${baseUrl}/bundle`, new CreateBundleDto(name, maps));
  }

  deleteBundle(id: number) {
    return this.http.delete(`${baseUrl}/bundle/${id}`);
  }
}
