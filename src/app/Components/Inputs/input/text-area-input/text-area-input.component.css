.container {
  width: 100%;

}

.container p {
  color: var(--background-color-dark);
}

.container textarea {
  margin: 10px 0;
  box-sizing: border-box;

  border-radius: 25px;
  border: 1px solid transparent;
  padding: var(--components-vertical-padding) calc(var(--components-horizontal-padding) + 1.5rem) var(--components-vertical-padding) var(--components-horizontal-padding); /* Adjusted right padding */

  color: var(--background-color-dark);
  background-color: var(--background-color);

  max-width: 100%;
  width: 100%;
}

.container textarea:focus {
  border: var(--background-color-dark) 1px solid;
}

.container textarea:hover {
  border: var(--background-color-dark) 1px solid;
}

textarea::-webkit-scrollbar {
  width: 8px;
}

textarea::-webkit-scrollbar-track {
  background-color: transparent;
}

textarea::-webkit-scrollbar-thumb {
  background-color: var(--background-color-dark);
}
