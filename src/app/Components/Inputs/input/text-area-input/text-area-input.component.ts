import {Component, Input} from '@angular/core';
import {ControlValueAccessor, FormsModule, NG_VALUE_ACCESSOR} from "@angular/forms";

@Component({
  selector: 'app-text-area-input',
  standalone: true,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: TextAreaInputComponent,
      multi: true
    }
  ],
  imports: [
    FormsModule
  ],
  templateUrl: './text-area-input.component.html',
  styleUrl: './text-area-input.component.css'
})
export class TextAreaInputComponent implements ControlValueAccessor {
  @Input() label: string = "";
  value: string = "";
  @Input() placeholder: string = "";

  onChange: any = () => {
  };
  onTouch: any = () => {
  };
  protected isDisabled: boolean = false;


  onInput(event: Event) {
    const inputElement = event.target as HTMLInputElement;
    this.value = inputElement.value;
    this.onChange(this.value);
    this.onTouch();
  }


  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouch = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.isDisabled = isDisabled;
  }

  writeValue(value: string): void {
    this.value = value;
  }
}
