<div class="container">
  <p>{{ label }}</p>
  <div class="size-container">
    <input
      (input)="onHeightInput($event)"
      [disabled]="isDisabled"
      placeholder="height (squares)"
      type="number"
      [value]="mapSize.heightSquares"
    />
    x
    <input
      (input)="onWidthInput($event)"
      [disabled]="isDisabled"
      placeholder="width (squares)"
      type="number"
      [value]="mapSize.widthSquares"
    />
    <input
      (input)="onFtInput($event)"
      [disabled]="isDisabled"
      placeholder="ft"
      type="number"
      [value]="mapSize.squareSideLength"
    />
  </div>
</div>
