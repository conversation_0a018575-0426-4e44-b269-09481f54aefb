import {Component, EventEmitter, forwardRef, Input, Output} from '@angular/core';
import {ControlValueAccessor, NG_VALUE_ACCESSOR} from "@angular/forms";
import {MapSizeDto} from "../../../../Dtos/map-size.dto";

@Component({
  selector: 'app-size-input',
  standalone: true,
  imports: [],
  templateUrl: './size-input.component.html',
  styleUrl: './size-input.component.css',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => SizeInputComponent),
      multi: true,
    },
  ],
})
export class SizeInputComponent implements ControlValueAccessor {
  @Input() label: string = "";
  /**
   * The type of the input field. Can be text, password, email, etc.
   */
  @Input() mapSize: MapSizeDto = new MapSizeDto(null, null, null);
  @Input() isDisabled: boolean = false;
  @Output() mapSizeChange: EventEmitter<MapSizeDto> = new EventEmitter<MapSizeDto>();

  onChange: any = () => {
  };
  onTouch: any = () => {
  };

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouch = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.isDisabled = isDisabled;
  }

  writeValue(value: MapSizeDto): void {
    this.mapSize = value;
  }

  onHeightInput($event: Event) {
    const inputElement = $event.target as HTMLInputElement;
    this.mapSize.heightSquares = +inputElement.value;
    this.onChange(this.mapSize.heightSquares);
    this.onTouch();
  }
  onWidthInput($event: Event) {
    const inputElement = $event.target as HTMLInputElement;
    this.mapSize.widthSquares = +inputElement.value;
    this.onChange(this.mapSize.widthSquares);
    this.onTouch();
  }
  onFtInput($event: Event) {
    const inputElement = $event.target as HTMLInputElement;
    this.mapSize.squareSideLength = +inputElement.value;
    this.onChange(this.mapSize.squareSideLength);
    this.onTouch();
  }


}
