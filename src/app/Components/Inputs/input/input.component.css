.container {
  width: 100%;

}

.container p {
  color: var(--background-color-dark);
}

.container input {
  margin: 10px 0;
  box-sizing: border-box;

  border-radius: 25px;
  border: 1px solid transparent;
  padding: var(--components-vertical-padding) calc(var(--components-horizontal-padding) + 1.5rem) var(--components-vertical-padding) var(--components-horizontal-padding); /* Adjusted right padding */

  color: var(--background-color-dark);
  background-color: var(--background-color);
}

.container input:focus {
  border: var(--background-color-dark) 1px solid;
}

.container input:hover {
  border: var(--background-color-dark) 1px solid;
}
