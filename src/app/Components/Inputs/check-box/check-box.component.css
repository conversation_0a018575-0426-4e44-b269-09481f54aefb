.container {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 0.5rem;
}

input[type="checkbox"] {
  width: 28px;
  height: 28px;
  -webkit-appearance: none; /* Remove default styling for Webkit browsers */
  -moz-appearance: none; /* Remove default styling for Firefox */
  appearance: none;
  background-color: var(--background-color);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

input[type="checkbox"]:checked {
  background-color: var(--background-color);
  background-image: url('/check.svg');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 18px 18px;
  border: var(--background-color-dark) 1px solid;
}

input[type="checkbox"]:hover {
  border: var(--background-color-dark) 1px solid;
}

label {
  color: var(--white);
  font-size: 1.1rem;
  cursor: pointer;
  user-select: none;
}
