import {Component, Input} from '@angular/core';
import {NgClass} from "@angular/common";

@Component({
  selector: 'app-close-button',
  standalone: true,
  imports: [
    NgClass
  ],
  templateUrl: './close-button.component.html',
  styleUrl: './close-button.component.css'
})
export class CloseButtonComponent {
  /**
   * The color of the button. Can be either 'accent' or 'primary'.
   */
  @Input() color: string = 'accent';

  isAccent(): boolean {
    return this.color === 'accent';
  }

  isPrimary() {
    return this.color === 'primary';
  }
}
