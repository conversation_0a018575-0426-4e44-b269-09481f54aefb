import {Component, EventEmitter, Output} from '@angular/core';

@Component({
  selector: 'app-delete-button',
  standalone: true,
  imports: [],
  templateUrl: './delete-button.component.html',
  styleUrl: './delete-button.component.css'
})
export class DeleteButtonComponent {
  @Output() onClick: EventEmitter<any> = new EventEmitter<any>();

  handleClick($event: Event): void {
    $event.stopPropagation();
    this.onClick.emit($event);
  }
}
