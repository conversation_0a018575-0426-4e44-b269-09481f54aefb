import {Component, EventEmitter, Input, Output} from '@angular/core';
import {NgClass} from "@angular/common";

@Component({
  selector: 'app-star-toggler',
  standalone: true,
  imports: [
    NgClass
  ],
  templateUrl: './star-toggler.component.html',
  styleUrl: './star-toggler.component.css'
})
export class StarTogglerComponent {
  @Input() isStarred!: boolean;
  @Output() onToggle = new EventEmitter<boolean>();

  toggleStar(event: MouseEvent) {
    event.stopPropagation();
    event.preventDefault();
    this.onToggle.emit(!this.isStarred);
  }
}
