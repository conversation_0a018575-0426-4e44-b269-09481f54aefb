button {
  background-color: var(--accent-color);
  color: var(--background-color-dark);
  padding: 0.6rem 1.5rem;
  border: none;
  cursor: pointer;
  font-size: 1.1rem;
  border-radius: 12px;
  box-sizing: border-box;
}

button:hover {
  filter: brightness(0.9);
}

.red {
  background-color: var(--active-red);
  color: var(--white);
}

.red:hover {
  filter: brightness(0.9);
}

.green {
  background-color: var(--active-green);
  color: var(--white);
}

.green:hover {
  filter: brightness(0.9);
}

.mini {
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
}
