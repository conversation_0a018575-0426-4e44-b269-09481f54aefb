import {Component, Input} from '@angular/core';
import {StarTogglerComponent} from "../star-toggler/star-toggler.component";
import {MapService} from "../../../../Services/map.service";
import {IsMapFavorite} from "../../../../Dtos/is-map-favorite";

@Component({
  selector: 'app-favorite-toggler',
  standalone: true,
  imports: [
    StarTogglerComponent
  ],
  templateUrl: './favorite-toggler.component.html',
  styleUrl: './favorite-toggler.component.css'
})
export class FavoriteTogglerComponent {
  @Input() map!: IsMapFavorite;

  constructor(private mapService: MapService) {
  }

  toggleFavorite() {
      this.mapService.toggleFavorite(this.map.id, !this.map.isFavorite).subscribe({
        next: () => this.map.isFavorite = !this.map.isFavorite
      });
  }
}
