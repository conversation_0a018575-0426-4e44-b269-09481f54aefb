import {Component, EventEmitter, Input, Output} from '@angular/core';
import {NgIf} from "@angular/common";

@Component({
  selector: 'app-option-button',
  standalone: true,
  imports: [
    NgIf
  ],
  templateUrl: './option-button.component.html',
  styleUrl: './option-button.component.css'
})
export class OptionButtonComponent {
  @Output() eventEmitter: EventEmitter<any> = new EventEmitter();
  @Input() label: string = "";
  @Input() title: string | undefined;

  clicked() {
    this.eventEmitter.emit();
  }
}
