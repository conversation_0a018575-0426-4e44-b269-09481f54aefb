import {Component, EventEmitter, Input, model, OnInit, Output, signal} from '@angular/core';

import {FormsModule} from '@angular/forms';
import {MatAutocompleteModule, MatAutocompleteSelectedEvent} from '@angular/material/autocomplete';
import {MatChipInputEvent, MatChipsModule} from '@angular/material/chips';
import {MatFormFieldModule} from '@angular/material/form-field';
import {MatIconModule} from '@angular/material/icon';
import {TagDto} from "../../../Dtos/tag.dto";
import {NotificationService} from "../../../Services/notification.service";
import {COMMA, ENTER} from "@angular/cdk/keycodes";
import {TagService} from "../../../Services/tags.service";
import {firstValueFrom} from "rxjs";
import {NgForOf} from "@angular/common";

@Component({
  selector: 'app-tags-search-bar',
  standalone: true,
  imports: [
    MatIconModule,
    MatChipsModule,
    MatFormFieldModule,
    MatAutocompleteModule,
    FormsModule,
    NgForOf
  ],
  templateUrl: './tags-search-bar.component.html',
  styleUrl: './tags-search-bar.component.css'
})
export class TagsSearchBarComponent implements OnInit {
  @Input() allowNewTags: boolean = false;
  @Output() public tagsChangedEventEmitter = new EventEmitter<TagDto[]>();

  readonly currentTag = model({} as TagDto | null);
  readonly tags = signal([] as TagDto[]);
  readonly separatorKeysCodes: number[] = [ENTER, COMMA];
  private allTags = signal([] as TagDto[]);
  private currentInputValue: string = '';

  constructor(private notificationService: NotificationService, private tagService: TagService) {
  }

  async ngOnInit() {
    this.allTags.set(await firstValueFrom(this.tagService.getTags()));
  }

  selected($event: MatAutocompleteSelectedEvent) {
    this.add({value: $event.option.viewValue} as MatChipInputEvent);
  }

  remove(tag: TagDto): void {
    this.tags.update(tags => {
      const index = tags.indexOf(tag);
      if (index < 0) {
        return tags;
      }
      tags.splice(index, 1);
      return [...tags];
    });
    this.tagsChangedEventEmitter.emit(this.tags());
  }

  add(event: MatChipInputEvent): void {
    let value = (event.value || '').trim();

    if (value) {
      value = value.charAt(0).toUpperCase() + value.slice(1).toLowerCase();
      if (this.tags().some(tag => tag.name === value)) {
        this.notificationService.showInfo(`Tag ${value} already added`);
        return
      }
      this.tagService.getTags().subscribe(tags => {
        const tag = tags.find(t => t.name === value);
        if (tag) {
          this.tags.update(tags => [...tags, tag]);
          this.tagsChangedEventEmitter.emit(this.tags());
        } else if (this.allowNewTags) {
          this.tags.update(tags => [...tags, new TagDto(value)]);
          this.tagsChangedEventEmitter.emit(this.tags());
        } else {
          this.notificationService.showInfo(`Tag ${value} not found`);
        }
      });
    }

    this.currentTag.set(null);
  }

  filterOutExistingTags(tags: TagDto[]) {
    return tags.filter(tag => !this.tags().some(selectedTag => selectedTag.name === tag.name));
  }

  filterOutInputValue(tags: TagDto[]):TagDto[] {
    if (!this.currentInputValue) {
      return tags;
    }
    return tags.filter(tag => tag.name.toLowerCase().includes(this.currentInputValue.toLowerCase()));
  }

  getFilteredTags() {
    let allTags = this.allTags();
    let filteredTags = this.filterOutExistingTags(allTags);
    return this.filterOutInputValue(filteredTags);
  }

  setCurrentInputValue($event: Event) {
    const inputElement = $event.target as HTMLInputElement;
    const value = inputElement.value;
    this.currentInputValue = inputElement.value.charAt(0).toUpperCase() + value.slice(1).toLowerCase();
  }
}
