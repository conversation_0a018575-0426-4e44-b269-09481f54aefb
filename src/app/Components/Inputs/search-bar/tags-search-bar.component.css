:host{
  position: relative;
  height: 3rem;
}

.chip-form{
  position: absolute;
  width: 100%;
  height: 4rem;
  top: 50%;
  transform: translateY(-50%);
}

.chip-form input{
  position: absolute;
  margin-left: 0;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 24%;

  text-wrap: nowrap;
  text-overflow: ellipsis;

  border-radius: 25px;
  border: 3px solid #74777F;
  padding: var(--components-vertical-padding) calc(var(--components-horizontal-padding) + 1.5rem) var(--components-vertical-padding) var(--components-horizontal-padding); /* Adjusted right padding */

  background: var(--background-color) url('/search.svg') no-repeat right var(--components-horizontal-padding) center;
  background-size: 1rem;

  box-sizing: border-box;
  color: var(--background-color-dark);
}

.chip-form input:focus {
  border: var(--background-color-dark) 1px solid;
  background: var(--background-color) url('/search.svg') no-repeat right var(--components-horizontal-padding) center;
  background-size: 1rem;
}

.chip-form input:hover {
  border: var(--background-color-dark) 1px solid;
}

.chip-grid{
  position: absolute;
  top: 0;
  right: 2rem;
}

.chip-form ::ng-deep .mat-mdc-text-field-wrapper{
  position: relative;
  padding: 0;
  height: 2rem;
  top: 50%;
  transform: translateY(-50%);
}

.chip-form ::ng-deep .mat-mdc-text-field-wrapper .mat-mdc-form-field-flex .mat-mdc-form-field-infix{
  position: initial;
}

.chip-form ::ng-deep .mat-mdc-text-field-wrapper .mat-mdc-form-field-flex .mat-mdc-form-field-infix .mat-mdc-chip-grid{
  margin: 0 0.6rem 0 0;
  top: 50%;
  transform: translateY(-50%);
  overflow: hidden;
  max-width: 70%;
}
.chip-form ::ng-deep .mat-mdc-text-field-wrapper .mat-mdc-form-field-flex .mat-mdc-form-field-infix .mat-mdc-chip-grid .mdc-evolution-chip-set__chips{
  flex-flow: unset;
  flex-direction: row-reverse;
}
