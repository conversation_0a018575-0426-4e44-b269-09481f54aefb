<mat-form-field class="chip-form" appearance="outline">
  <input
    name="tags"
    placeholder="Search {{allowNewTags?'and create ':null}}tags..."
    [matChipInputFor]="chipGrid"
    [matAutocomplete]="auto"
    [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
    [matChipInputAddOnBlur]="true"
    (matChipInputTokenEnd)="add($event)"
    (input)="setCurrentInputValue($event)"
  />
  <mat-chip-grid #chipGrid aria-label="Tag selection" class="chip-grid">
    @for (tag of tags(); track tag) {
      <mat-chip-row
        color="warn"
        (removed)="remove(tag)"
        [removable]="true"
        [aria-description]="'press enter to edit ' + tag.name"
      >
        {{ tag.name }}
        <button matChipRemove [attr.aria-label]="'remove ' + tag.name">
          <mat-icon>cancel</mat-icon>
        </button>
      </mat-chip-row>
    }
  </mat-chip-grid>
  <mat-autocomplete #auto="matAutocomplete" (optionSelected)="selected($event)">
    <mat-option *ngFor="let tag of getFilteredTags()" [value]="tag">
      {{ tag.name }}
    </mat-option>
  </mat-autocomplete>
</mat-form-field>

