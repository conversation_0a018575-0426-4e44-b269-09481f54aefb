import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {NgForOf} from "@angular/common";
import {FormsModule} from "@angular/forms";

@Component({
  selector: 'app-selector',
  standalone: true,
  imports: [
    NgForOf,
    FormsModule
  ],
  templateUrl: './selector.component.html',
  styleUrl: './selector.component.css'
})
export class SelectorComponent implements OnInit {
  @Input() options!: Option[];
  selected: Option | undefined;
  @Output() selectOptionEvent = new EventEmitter<Option>();

  ngOnInit() {
    if (this.options && this.options.length > 0)
      this.selected = this.options[0];
    else
      throw new Error('Options must be provided for the selector');

    this.selectOptionEvent.emit(this.selected);
  }

  onOptionSelected($event: Event) {
    let target = $event.target as HTMLSelectElement;
    let selectedValue = target.value;
    let selectedOption = this.options.find(option => option.value === selectedValue);
    if (selectedOption) {
      this.selected = selectedOption;
      this.selectOptionEvent.emit(selectedOption);
    }
  }
}

export class Option {
  value: string = '';
  label: string = '';

  constructor(value: string, label: string) {
    this.value = value;
    this.label = label;
  }
}
