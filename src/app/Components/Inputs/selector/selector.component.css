select {
  -webkit-appearance: none; /* Safari and Chrome */
  -moz-appearance: none; /* Firefox */
  appearance: none;

  background: var(--background-color) url('/arrow.svg') no-repeat scroll right var(--components-horizontal-padding) center;
  background-size: 1rem;
  padding: var(--components-vertical-padding) 3rem var(--components-vertical-padding) var(--components-horizontal-padding);

  border-radius: 25px;
  border: 1px solid transparent;
  color: var(--background-color-dark);

  cursor: pointer;
}

select:focus {
  outline: none;
  border: var(--background-color-dark) 1px solid;
}

select:hover {
  border: var(--background-color-dark) 1px solid;
}

option {
  background-color: var(--background-color);
  color: var(--background-color-dark);
}
