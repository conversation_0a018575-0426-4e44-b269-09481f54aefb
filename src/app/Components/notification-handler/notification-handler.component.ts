import {Component, OnInit} from '@angular/core';
import {NotificationComponent, NotificationType} from "./notification/notification.component";
import {NotificationService} from "../../Services/notification.service";
import {NgForOf} from "@angular/common";


@Component({
  selector: 'app-notification-handler',
  standalone: true,
  imports: [
    NotificationComponent,
    NgForOf
  ],
  templateUrl: './notification-handler.component.html',
  styleUrl: './notification-handler.component.css'
})
export class NotificationHandlerComponent implements OnInit {
  notifications: { message: string, type: NotificationType }[] = [];
  protected readonly Array = Array;

  constructor(private notificationService: NotificationService) {
  }

  ngOnInit(): void {
    this.notificationService.errorMessages.subscribe((message) => {
      this.addNotification(message, NotificationType.ERROR);
    });

    this.notificationService.infoMessages.subscribe((message) => {
      this.addNotification(message, NotificationType.INFO);
    });

    this.notificationService.successMessages.subscribe((message) => {
      this.addNotification(message, NotificationType.SUCCESS);
    });
  }

  /**
   * Add a new notification to the list of notifications
   * @param message
   * @param type
   */
  addNotification(message: string, type: NotificationType): void {
    if (!this.notifications.some((notification) => notification.message === message))
      this.notifications.push({message, type});
  }

  removeNotification(message: string): void {
    this.notifications.splice(this.notifications.findIndex((notification) => notification.message === message), 1);
  }
}
