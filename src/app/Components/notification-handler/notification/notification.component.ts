import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {Ng<PERSON>lass, NgIf} from "@angular/common";
import {animate, style, transition, trigger} from "@angular/animations";
import {CloseButtonComponent} from "../../Inputs/buttons/close-button/close-button.component";

@Component({
  selector: 'app-notification',
  standalone: true,
  imports: [
    Ng<PERSON>lass,
    NgIf,
    CloseButtonComponent
  ],
  animations: [
    trigger('inAndOut', [
        transition(':leave', [animate(300, style({
          opacity: 0,
          transform: 'translateY(-20px)',
        }))]),
        transition(':enter', [style({
          opacity: 1,
          transform: 'translateY(-20px)',
        }), animate(300)])
      ]
    )
  ],
  templateUrl: './notification.component.html',
  styleUrl: './notification.component.css'
})
export class NotificationComponent implements OnInit {
  @Output() deleteNotification: EventEmitter<void> = new EventEmitter<void>();
  @Input() message!: string;
  @Input() type!: NotificationType;

  visible: boolean = true;
  timeout: any;
  protected readonly NotificationType = NotificationType;

  constructor() {
  }

  ngOnInit(): void {
    this.timeout = setTimeout(() => this.dismiss(), 5000);
  }

  dismiss() {
    this.visible = false;
    this.timeout = setTimeout(() => this.deleteNotification.emit(), 300);
  }

  closeButtonColor() {
    switch (this.type) {
      case NotificationType.ERROR:
        return 'primary';
      case NotificationType.SUCCESS:
        return 'primary';
      case NotificationType.INFO:
        return 'accent';
    }
  }
}

export enum NotificationType {
  ERROR,
  SUCCESS,
  INFO
}
