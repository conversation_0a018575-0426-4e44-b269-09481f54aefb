.notification-container {
  display: flex;
  width: 100%;
  word-wrap: break-word;
  text-align: left;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  border-radius: 6px;
  border: 1px solid;
  color: var(--background-color-dark);
}

p {
  margin: 0;
  word-break: break-word;
}

.error {
  background-color: var(--active-red);
  border-color: var(--background-color-dark);
}

.success {
  background-color: var(--active-green);
  border-color: var(--background-color-dark)
}

.info {
  background-color: var(--background-color-deemed);
  border-color: var(--accent-color);
}

app-close-button {
  width: 1.6rem;
  height: 1.6rem;
  flex-shrink: 0;
}
