import {Component, OnInit} from '@angular/core';
import {OptionButtonComponent} from "../Inputs/buttons/option-button/option-button.component";
import {NgForOf, NgIf} from "@angular/common";
import {MapDetailsDto} from "../../Dtos/map-details.dto";
import {MapService} from "../../Services/map.service";
import {AuthService} from "../../Services/auth.service";
import {ActivatedRoute} from "@angular/router";
import {NotificationService} from "../../Services/notification.service";
import {FavoriteTogglerComponent} from "../Inputs/buttons/favorite-toggler/favorite-toggler.component";
import {MatIcon} from "@angular/material/icon";
import {ManageBundledMapsComponent} from "../Galleries/manage-maps/manage-bundeled-maps/manage-bundeled-maps.component";


@Component({
  selector: 'app-map-view',
  standalone: true,
  imports: [
    NgI<PERSON>,
    FavoriteTogglerComponent,
    MatIcon,
    NgForOf,
    Option<PERSON>uttonComponent,
    ManageBundledMapsComponent
  ],
  templateUrl: './map-view.component.html',
  styleUrl: './map-view.component.css'
})
export class MapViewComponent implements OnInit {
  map!: MapDetailsDto;
  protected isLoaded: boolean = false;

  constructor(protected mapService: MapService, protected authService: AuthService, private activatedRoute: ActivatedRoute,
              private notificationService: NotificationService) {
  }

  ngOnInit(): void {
    this.activatedRoute.paramMap.subscribe(params => {
      let id = params.get('id');
      if (id == null) {
        this.notificationService.showError('Map not found');
        return;
      }

      this.mapService.getMapDetails(+id).subscribe((map: MapDetailsDto) => this.map = map);
    });
  }

  onLoad() {
    this.isLoaded = true;
  }
}
