.view {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.container {
  height: 80vh;
  width: 80vw;
  background-color: var(--background-color-deemed);
  display: flex;
  justify-content: space-between;
  border-radius: 30px;
  overflow: hidden;
}

.map{
  position: relative;
  opacity: 0;
}

.map img{
  height: 100%;
  object-fit: cover;
}

.map app-favorite-toggler{
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
}

.details{
  width: 100%;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  overflow-y: auto;
  opacity: 0;
}

::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

::-webkit-scrollbar-track {
  background: #b6b6b6;
}

::-webkit-scrollbar-thumb {
  background: var(--accent-color-dark);
  border-radius: 6px;
  border: 0;
}

.header{
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap-reverse;
}

.header h1{
  overflow-wrap: anywhere;
}

.tags{
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  border: 2px dashed var(--background-color-dark);
  padding: 0 0.5rem;
  border-radius: 20px;
}

.loaded{
  transition: opacity 2s;
  opacity: 1;
}

.description p{
  overflow-wrap: anywhere;
}

.header .right-container{
  display: flex;
  gap: 2rem;
}

.header mat-icon{
  cursor: pointer;
  transform: scale(1.5) translateX(-25%);
}
