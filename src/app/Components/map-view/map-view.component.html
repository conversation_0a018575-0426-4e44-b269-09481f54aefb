<div class="view">
  <div *ngIf="map" class="container">
    <div [class.loaded]="isLoaded" class="map">
      <img (load)="onLoad()" [src]="map.url" alt="Map is missing">
      <app-favorite-toggler
        *ngIf="!authService.isCreator()"
        [map]="map"
      ></app-favorite-toggler>
    </div>
    <div [class.loaded]="isLoaded" class="details">
      <div class="header">
        <h1>{{ map.name }}</h1>
        <div class="right-container">
          <h2 *ngIf="map.size">{{ map.size.heightSquares }}x{{ map.size.heightSquares }}, {{ map.size.squareSideLength }}ft</h2>
          <a [href]="map.url">
            <mat-icon>cloud_download</mat-icon>
          </a>
        </div>
      </div>
      <div *ngIf="map.tags.length>0" class="tags">
        <app-option-button *ngFor="let tag of map.tags" [label]="tag.name"></app-option-button>
      </div>
      <div class="description">
        <p [style.white-space]="'pre-line'">{{ map.description }}</p>
      </div>
      <div class="footer">
        <h2>Maps from shared bundles:</h2>
        <app-manage-bundled-maps [itemsInRow]="2" [mapId]="map.id" [canMapItemDelete]="false">
        </app-manage-bundled-maps>
      </div>
    </div>
  </div>
</div>
