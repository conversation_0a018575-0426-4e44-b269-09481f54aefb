import {Component} from '@angular/core';
import {PopUpFormComponent} from "../pop-up-form.component";
import {InputComponent} from "../../../Inputs/input/input.component";
import {FormsModule} from "@angular/forms";
import {OptionButtonComponent} from "../../../Inputs/buttons/option-button/option-button.component";
import {Router} from "@angular/router";
import {BundleService} from "../../../../Services/bundle.service";
import {NotificationService} from "../../../../Services/notification.service";
import {MatDialog, MatDialogRef} from "@angular/material/dialog";
import {SelectMapsPopUpFormComponent} from "../select-maps-pop-up-form/select-maps-pop-up-form.component";
import {ManageMapItemDto} from "../../../../Dtos/manage-map-item.dto";

@Component({
  selector: 'app-create-bundle-pop-up-form',
  standalone: true,
  imports: [
    PopUpFormComponent,
    InputComponent,
    FormsModule,
    OptionButtonComponent
  ],
  templateUrl: './create-bundle-pop-up-form.component.html',
  styleUrl: './create-bundle-pop-up-form.component.css'
})
export class CreateBundlePopUpFormComponent {
  bundleName: string | null = null;
  maps: number[] = [];

  constructor(public router: Router, public bundleService: BundleService, public notificationService: NotificationService,
              private dialogRef: MatDialogRef<SelectMapsPopUpFormComponent>, private dialog: MatDialog) {
  }

  createBundle() {
    if (this.maps.length === 0) {
      this.notificationService.showError('At least one map must be selected');
      return;
    }

    this.bundleService.createBundle(this.bundleName, this.maps).subscribe(
      {
        next: () => {
          this.notificationService.showSuccess('Bundle created successfully');
          window.location.reload();
          this.dialogRef.close();
        }
      }
    );
  }

  openAddMaps() {
    const dialog = this.dialog.open(SelectMapsPopUpFormComponent, {
      width: '80vw',
      maxWidth: '100vw',
      backdropClass: 'custom-backdrop',
      disableClose: true,
    });
    dialog.backdropClick().subscribe(() => {
      if (confirm("Discard changes?")) dialog.close();
    });
    dialog.afterClosed().subscribe((selectedMaps: ManageMapItemDto[]) => {
      if (selectedMaps) {
        this.maps = selectedMaps.map(map => map.id);
        this.notificationService.showSuccess(this.maps.length + ' maps were added to the bundle');
      } else {
        this.notificationService.showError('No maps selected');
      }
    });
  }
}
