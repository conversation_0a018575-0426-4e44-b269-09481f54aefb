import {Component, EventEmitter, Output, ViewChild} from '@angular/core';
import {PopUpFormComponent} from "../pop-up-form.component";
import {MatDialogModule, MatDialogRef} from "@angular/material/dialog";
import {ManageMapItemDto} from "../../../../Dtos/manage-map-item.dto";
import {
  ManageCreatorMapsComponent
} from "../../../Galleries/manage-maps/manage-creator-maps/manage-creator-maps.component";

@Component({
  selector: 'app-select-maps-pop-up-form',
  standalone: true,
  imports: [
    PopUpFormComponent,
    MatDialogModule,
    ManageCreatorMapsComponent
  ],
  templateUrl: './select-maps-pop-up-form.component.html',
  styleUrl: './select-maps-pop-up-form.component.css'
})
export class SelectMapsPopUpFormComponent {
  constructor(private dialogRef: MatDialogRef<SelectMapsPopUpFormComponent>) {}

  @ViewChild(ManageCreatorMapsComponent) manageMapsComponent!: ManageCreatorMapsComponent;
  @Output() fetchMapItems = new EventEmitter<ManageMapItemDto[]>();

  applySelection() {
    this.dialogRef.close(this.manageMapsComponent.items.filter(item => item.isSelected));
  }
}
