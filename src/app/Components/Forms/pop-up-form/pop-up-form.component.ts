import {Component, EventEmitter, Input, Output} from '@angular/core';
import {MatDialogModule, MatDialogRef} from "@angular/material/dialog";
import {ButtonComponent} from "../../Inputs/buttons/button/button.component";
import {CloseButtonComponent} from "../../Inputs/buttons/close-button/close-button.component";

@Component({
  selector: 'app-pop-up-form',
  standalone: true,
  imports: [
    MatDialogModule,
    ButtonComponent,
    CloseButtonComponent,
  ],
  templateUrl: './pop-up-form.component.html',
  styleUrl: './pop-up-form.component.css'
})
export class PopUpFormComponent {
  @Output() onSubmit: EventEmitter<any> = new EventEmitter<any>;
  @Input() submitButtonLabel: string = 'Save';
  @Input() title: string | undefined;
  @Input() isUploading: boolean = false;

  constructor(private dialogRef: MatDialogRef<PopUpFormComponent>) {}

  discardForm() {
    if (confirm('Discard changes?'))
      this.dialogRef.close();
  }
}
