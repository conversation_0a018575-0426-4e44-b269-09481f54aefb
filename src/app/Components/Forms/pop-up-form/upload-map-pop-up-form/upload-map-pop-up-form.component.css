.drop-zone {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  padding: 0.5rem;
  width: 100%;
  box-sizing: border-box;
  gap: 1rem;
  height: 30vh;

  border: 2px dashed var(--accent-color-dark);
  border-radius: 30px;
}

.map-preview{
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: start;
  padding: 1rem;
  box-sizing: border-box;
  height: fit-content;
}

.dragover {
  border-color: var(--accent-color);
}

.label {
  color: #555;
  overflow: hidden;
  white-space: wrap;
  text-overflow: ellipsis;
  max-width: 100%;
  text-align: center;
}

.drop-zone-button {
  display: inline-block;
  padding: 0.5rem 1rem;
  background-color: var(--accent-color);
  color: var(--background-color-dark);
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
}

.map-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 30px;
}

.remove-preview-button{
  position: absolute;
  top: 2rem;
  right: 2rem;
}

.file-input {
  display: none; /* Hide the original file input */
}

app-tags-search-bar{
  margin-bottom: 2rem;
  margin-top: 1rem;
}
