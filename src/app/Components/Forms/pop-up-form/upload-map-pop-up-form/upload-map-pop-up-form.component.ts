import {AfterViewInit, Component, ViewChild} from '@angular/core';
import {MapService} from "../../../../Services/map.service";
import {NotificationService} from "../../../../Services/notification.service";
import {MapDto} from "../../../../Dtos/map.dto";
import {MatDialogModule} from '@angular/material/dialog';
import {PopUpFormComponent} from "../pop-up-form.component";
import {NgIf} from "@angular/common";
import {ButtonComponent} from "../../../Inputs/buttons/button/button.component";
import {InputComponent} from "../../../Inputs/input/input.component";
import {TextAreaInputComponent} from "../../../Inputs/input/text-area-input/text-area-input.component";
import {SizeInputComponent} from "../../../Inputs/input/size-input/size-input.component";
import {FormsModule} from "@angular/forms";
import {MapSizeDto} from "../../../../Dtos/map-size.dto";
import {constants, patterns} from "../../../../app.config";
import {TagsSearchBarComponent} from "../../../Inputs/search-bar/tags-search-bar.component";
import {TagDto} from "../../../../Dtos/tag.dto";

@Component({
  selector: 'app-upload-map-pop-up-form',
  standalone: true,
  imports: [
    MatDialogModule,
    PopUpFormComponent,
    NgIf,
    ButtonComponent,
    InputComponent,
    TextAreaInputComponent,
    SizeInputComponent,
    FormsModule,
    TagsSearchBarComponent
  ],
  templateUrl: './upload-map-pop-up-form.component.html',
  styleUrl: './upload-map-pop-up-form.component.css'
})
export class UploadMapPopUpFormComponent implements AfterViewInit{
  file: File | null = null;
  protected previewUrl: string | ArrayBuffer | null = null;
  mapName: string = '';
  description: string = '';
  mapSize: MapSizeDto | null = new MapSizeDto(null, null, null);
  isUploading = false;
  tags: TagDto[] = [];
  @ViewChild(TagsSearchBarComponent) tagsSelector!: TagsSearchBarComponent | undefined;

  constructor(private notificationService: NotificationService, private mapService: MapService) {
  }


  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    const file = input.files?.item(0);

    if (file) {
      this.attachFile(file);
    } else {
      this.notificationService.showError('No file selected');
    }
  }

  onDragOver(event: DragEvent): void {
    event.preventDefault();
    const target = event.currentTarget as HTMLElement;
    target.classList.add('dragover');
  }

  onDragLeave(event: DragEvent): void {
    const target = event.currentTarget as HTMLElement;
    target.classList.remove('dragover');
  }

  onDrop(event: DragEvent): void {
    event.preventDefault();
    const target = event.currentTarget as HTMLElement;
    target.classList.remove('dragover');

    if (event.dataTransfer && event.dataTransfer.files.length > 0) {
      if (event.dataTransfer.files.length > 1) {
        this.notificationService.showError('You can only upload one file at a time');
        return;
      }
      this.attachFile(event.dataTransfer.files.item(0)!);
    }
  }

  attachFile(file: File): void {
    const customTextElement = document.querySelector('.label');
    if (customTextElement) {
      if (!file.type.startsWith('image/png') && !file.type.startsWith('image/jpeg') && !file.type.startsWith('image/jpg')) {
        this.notificationService.showError('Only PNG and JPEG files are supported');
        return;
      }
      const reader = new FileReader();

      reader.onload = () => {
        this.previewUrl = reader.result;
      };

      reader.readAsDataURL(file);

      customTextElement.textContent = file.name;
      this.file = file;
    }
  }

  removeFile() {
    this.file = null;
    this.previewUrl = null;
  }

  createMap() {
    if (!this.validateMap())
      return;

    this.mapService.uploadMap(this.file!, this.mapName, this.description, this.mapSize, this.tags).subscribe({
      next: (map: MapDto) => {
        this.notificationService.showSuccess(`Map ${map.name} uploaded successfully`);
        window.location.reload();
      }
    });
    this.isUploading = true;
  }

  validateMapSize(): boolean {
    if (this.mapSize) {
      if (this.mapSize.heightSquares == 0) {
        this.mapSize.heightSquares = null;
      }
      if (this.mapSize.widthSquares == 0) {
        this.mapSize.widthSquares = null;
      }
      if (this.mapSize.squareSideLength == 0) {
        this.mapSize.squareSideLength = null;
      }

      if (!this.mapSize.heightSquares && !this.mapSize.widthSquares && !this.mapSize.squareSideLength) {
        this.mapSize = null;
        return true;
      }

      if (!this.mapSize.heightSquares || !this.mapSize.widthSquares || !this.mapSize.squareSideLength) {
        if (this.mapSize.heightSquares || this.mapSize.widthSquares || this.mapSize.squareSideLength) {
          this.notificationService.showError('All map size fields or none must be filled');
          return false;
        }
      }
      if (this.mapSize.heightSquares && this.mapSize.widthSquares && this.mapSize.squareSideLength) {
        if (this.mapSize.heightSquares < 1 || this.mapSize.widthSquares < 1 || this.mapSize.squareSideLength < 1) {
          this.notificationService.showError('Map size fields must be positive integers');
          return false;
        }
      }
    }
    return true;
  }

  ngAfterViewInit(): void {
    this.tagsSelector!.tagsChangedEventEmitter.subscribe(tags => this.tags = tags);
  }

  private validateMap(): boolean {
    if (!this.file) {
      this.notificationService.showError('No file selected');
      return false;
    }
    if (!this.mapName) {
      this.notificationService.showError('No map name provided');
      return false;
    }

    if (!this.validateMapSize()) return false;

    if (this.mapName && !patterns.name.pattern.test(this.mapName)) {
      this.notificationService.showError(patterns.name.message);
      return false;
    }

    if (this.mapName.length > constants.maxNameLength) {
      this.notificationService.showError(`Name must be less than ${constants.maxNameLength} characters`);
      return false;
    }

    if (this.description.length > constants.maxDescriptionLength) {
      this.notificationService.showError(`Description must be less than ${constants.maxDescriptionLength} characters`);
      return false;
    }
    return true;
  }

}
