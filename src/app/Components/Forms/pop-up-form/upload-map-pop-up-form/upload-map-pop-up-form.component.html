<app-pop-up-form
  submitButtonLabel="Create"
  title="Upload new map"
  (onSubmit)="createMap()"
  [isUploading]="isUploading"
>
  <div *ngIf="!previewUrl"  class="drop-zone"
       (dragover)="onDragOver($event)"
       (dragleave)="onDragLeave($event)"
       (drop)="onDrop($event)">
    <span class="label">Drop your map in <span style="color: var(--accent-color-dark)">.png or .jpeg</span> format here</span>
    <span class="label">or</span>
    <label for="file-upload" class="drop-zone-button">Upload</label>
    <input id="file-upload" class="file-input" (change)="onFileSelected($event)" type="file">
  </div>
  <div *ngIf="previewUrl"  class="map-preview">
    <app-button
      class="remove-preview-button" (click)="removeFile()"
      label="Remove"
      [red]="true"
      [mini]="true"
    ></app-button>
    <img [src]="previewUrl" alt="Map preview">
  </div>

  <app-input
    label="Map name"
    placeholder="Input name"
    [(ngModel)]="mapName"
  >
  </app-input>
  <app-text-area-input
    label="Description"
    placeholder="Describe your unique scenario or what the map illustrates"
    [(ngModel)]="description"
  >
  </app-text-area-input>
  <app-tags-search-bar [allowNewTags]="true"></app-tags-search-bar>
  <app-size-input
    label="Size (optional)"
    [(mapSize)]="mapSize!"
  >
  </app-size-input>
</app-pop-up-form>
