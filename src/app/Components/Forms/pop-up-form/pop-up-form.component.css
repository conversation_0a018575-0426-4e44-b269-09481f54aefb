.container{
  background-color: var(--background-color-deemed);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: column;
  width: 100%;

  box-sizing: border-box;
  gap: 1rem;
  padding: 1rem;
  max-height: 98vh;
  overflow: hidden;
}

.container > *{
  width: 100%;
}

.header{
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header h2{
  font-size: 1.9rem;
}

.container .content{
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  overflow-y: auto;
  gap: 1rem;
  padding: 0.5rem;
}

.close{
  height: 1.8rem;
  width: 1.8rem;
}

.footer{
  display: flex;
  justify-content: center;
  align-items: center;
}
