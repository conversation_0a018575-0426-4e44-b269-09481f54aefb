import {Component} from '@angular/core';
import {InputComponent} from "../../Inputs/input/input.component";
import {ButtonComponent} from "../../Inputs/buttons/button/button.component";
import {AuthService} from "../../../Services/auth.service";
import {FormsModule} from "@angular/forms";
import {NotificationService} from "../../../Services/notification.service";
import {MatDialog} from "@angular/material/dialog";

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    InputComponent,
    ButtonComponent,
    FormsModule
  ],
  templateUrl: './login.component.html',
  styleUrl: './login.component.css'
})
export class LoginComponent {
  username: string = '';
  password: string = '';

  constructor(private authService: AuthService, private notificationService: NotificationService, private dialog: MatDialog) {
  }

  login() {
    this.authService.login(this.username, this.password).subscribe(
      {
        next: (user) => {
          this.notificationService.showSuccess('Hello, ' + user.username);
          this.dialog.closeAll();
        }
      }
    );
  }
}
