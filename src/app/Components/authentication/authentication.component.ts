import {Component} from '@angular/core';
import {NgIf} from "@angular/common";
import {LoginComponent} from "./login/login.component";
import {RegisterComponent} from "./register/register.component";

@Component({
  selector: 'app-authentication',
  standalone: true,
  imports: [
    LoginComponent,
    RegisterComponent,
    NgIf
  ],
  templateUrl: './authentication.component.html',
  styleUrl: './authentication.component.css'
})
export class AuthenticationComponent {
  isLogin: boolean = true;

  constructor() {
  }
}
