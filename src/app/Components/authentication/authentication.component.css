.container {
  display: flex;
  justify-content: start;
  flex-direction: column;
  background-color: var(--background-color-deemed);
  height: 500px;
}

.header {
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--white);
  background-color: var(--background-color-dark);
}

.header button {
  text-decoration: none;
  background-color: var(--background-color-dark);
  width: 50%;
  height: 3.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.4rem;
  color: var(--white);
}

.header .active {
  color: var(--accent-color);
  box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.5);;
}

.body {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}
