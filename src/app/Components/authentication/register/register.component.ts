import {Component} from '@angular/core';
import {ButtonComponent} from "../../Inputs/buttons/button/button.component";
import {InputComponent} from "../../Inputs/input/input.component";
import {AuthService} from "../../../Services/auth.service";
import {NotificationService} from "../../../Services/notification.service";
import {MatDialog} from "@angular/material/dialog";
import {FormsModule} from "@angular/forms";
import {Role} from "../../../Dtos/role";
import {Option, SelectorComponent} from "../../Inputs/selector/selector.component";
import {patterns} from "../../../app.config";

@Component({
  selector: 'app-register',
  standalone: true,
  imports: [
    ButtonComponent,
    InputComponent,
    FormsModule,
    SelectorComponent,
    SelectorComponent
  ],
  templateUrl: './register.component.html',
  styleUrl: './register.component.css'
})
export class RegisterComponent {
  username: string = '';
  password: string = '';
  repeatPassword: string = '';
  options = [
    new Option('CREATOR', 'Creator'),
    new Option('CUSTOMER', 'Customer')
  ];
  selectedRoleOption: Option | undefined;

  constructor(private authService: AuthService, private notificationService: NotificationService, private dialog: MatDialog) {}

  register() {
    if (!patterns.username.pattern.test(this.username)) {
      this.notificationService.showError(patterns.username.message);
      return;
    } else if (this.password !== this.repeatPassword) {
      this.notificationService.showError('Passwords do not match');
      return;
    } else if (!patterns.password.pattern.test(this.password)) {
      this.notificationService.showError(patterns.password.message);
      return;
    } else if (!this.selectedRoleOption?.value) {
      this.notificationService.showError('Account type is required');
      return;
    }

    this.authService.register(this.username, this.password, Role[this.selectedRoleOption?.value as keyof typeof Role]).subscribe(
      {
        next: (user) => {
          this.authService.login(this.username, this.password).subscribe(
            {
              next: (user) => {
                this.notificationService.showSuccess('Hello, ' + user.username);
                this.dialog.closeAll();
              },
              error: (error) => {
                this.notificationService.showError('Login failed');
              }
            }
          );
        },
        error: (error) => {
          this.notificationService.showError('Registration failed');
        }
      });
  }

  onOptionSelected($event: Option) {
    this.selectedRoleOption = $event;
  }
}
