<div class="container">
  <h1>Register</h1>
  <app-input [(ngModel)]="username" label="Username or email" placeholder="username"></app-input>
  <app-input [(ngModel)]="password" label="Password" placeholder="password" type="password"></app-input>
  <app-input [(ngModel)]="repeatPassword" label="Repeat password" placeholder="repeat password"
             type="password"></app-input>
  <div class="type-container">
    <span>Account type:</span>
    <app-selector (selectOptionEvent)="onOptionSelected($event)" [options]="options"></app-selector>
  </div>
  <app-button (click)="register()" label="Register"></app-button>
</div>
