import {Component, Input, OnInit} from '@angular/core';
import {MenuItemComponent} from "../../Inputs/buttons/menu-item/menu-item.component";
import {Router, RouterLink, RouterOutlet} from "@angular/router";
import {NgForOf} from "@angular/common";

@Component({
  selector: 'app-menu',
  standalone: true,
  imports: [
    MenuItemComponent,
    RouterLink,
    RouterOutlet,
    NgForOf
  ],
  templateUrl: './menu.component.html',
  styleUrl: './menu.component.css'
})
export class MenuComponent implements OnInit{
  @Input() items: MenuItem[] = [];
  activeRoute: string | undefined;

  constructor(private router: Router) {
  }

  ngOnInit(): void {
    this.router.events.subscribe(() => this.activeRoute = this.router.url);
  }
}

export class MenuItem {
  constructor(public label: string, public route: string, public events: { [key: string]: () => void } = {}) {
  }
}
