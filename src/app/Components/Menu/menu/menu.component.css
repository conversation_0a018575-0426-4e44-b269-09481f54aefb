:host {
  display: flex;
  padding: 0.5rem;
  gap: 0.25rem;
  position: relative;
  max-height: 100%;
  align-items: flex-start;
}

.menu {
  position: sticky;
  top: calc(var(--navbar-height) + var(--options-bar-height) + 1.3rem);

  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: center;

  width: 15%;
  gap: 0.5rem;
  padding: 0.5rem;
  box-sizing: border-box;
  min-width: fit-content;
}

:host ::ng-deep .menu ~ * {
  border-left: 2px solid var(--accent-color-dark);
}
