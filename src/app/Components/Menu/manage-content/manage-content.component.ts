import {Component, OnInit} from '@angular/core';
import {MenuComponent, MenuItem} from "../menu/menu.component";
import {OptionButtonComponent} from "../../Inputs/buttons/option-button/option-button.component";
import {OptionsPanelComponent} from "../../options-panel/options-panel.component";
import {AuthService} from "../../../Services/auth.service";
import {Role} from "../../../Dtos/role";
import {KeyValuePipe, NgForOf, NgIf} from "@angular/common";
import {NavigationEnd, Router} from "@angular/router";
import {filter} from "rxjs";

@Component({
  selector: 'app-manage-content',
  standalone: true,
  imports: [
    MenuComponent,
    OptionButtonComponent,
    OptionsPanelComponent,
    NgIf,
    NgForOf,
    KeyValuePipe
  ],
  templateUrl: './manage-content.component.html',
  styleUrl: './manage-content.component.css'
})
export class ManageContentComponent implements OnInit {
  protected menuItems: MenuItem[] = [];
  protected role: Role | undefined;
  protected selectedMenuItem: MenuItem | undefined;

  constructor(private authService: AuthService, private router: Router) {
  }

  ngOnInit() {
    this.role = this.authService.currentUserValue?.role;
    if (!this.role) return;

    switch (this.role) {
      case Role.CREATOR:
        this.menuItems = [
          new MenuItem('Maps', 'maps', {
            "create": () => this.router.navigate(['content/maps/upload'])
          }),
          new MenuItem('Bundles', 'bundles', {
            "create": () => this.router.navigate(['content/bundles/create'])})
        ];
        break;
    }

    this.router.events.pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        this.selectedMenuItem = ManageContentComponent.fetchFromUrl(this.menuItems, this.router.url);
      });

    this.selectedMenuItem = ManageContentComponent.fetchFromUrl(this.menuItems, this.router.url);
  }

  /**
   * Fetches current menu item from the given url
   * @param items to be searched
   * @param url to parse
   */
  static fetchFromUrl(items: MenuItem[], url: string): MenuItem | undefined {
    const urlSegments = url.split('/').filter(segment => !!segment);
    return items.find(item => urlSegments.includes(item.route));
  }

  protected readonly Role = Role;

  private getActiveRoute(activeRoute: string | undefined): string | undefined {
    return activeRoute;
  }
}
