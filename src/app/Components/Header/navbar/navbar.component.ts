import {Component, <PERSON><PERSON><PERSON>roy, OnInit} from '@angular/core';
import {RouterLink, RouterLinkActive} from "@angular/router";
import {UserDto} from "../../../Dtos/user.dto";
import {Role} from "../../../Dtos/role";
import {NotificationService} from "../../../Services/notification.service";
import {AuthService} from "../../../Services/auth.service";
import {Subscription} from "rxjs";

@Component({
  selector: 'app-navbar',
  standalone: true,
  imports: [
    RouterLink,
    RouterLinkActive
  ],
  templateUrl: './navbar.component.html',
  styleUrl: './navbar.component.css'
})
export class NavbarComponent implements OnInit, OnDestroy {
  user: UserDto | null = null;
  userSubscription: Subscription | undefined;

  constructor(private authService: AuthService, private errorService: NotificationService) {
  }

  async ngOnInit(): Promise<void> {
    this.userSubscription = this.authService.currentUser.subscribe(user => {
      this.user = user;
    });
    this.errorService.showError("This is a test notification message");
  }

  ngOnDestroy() {
    this.userSubscription?.unsubscribe();
  }

  isCustomer() {
    return this.user?.role === Role.CUSTOMER;
  }

  isCreator() {
    return this.user?.role === Role.CREATOR;
  }

  isAdmin() {
    return this.user?.role === Role.ADMIN;
  }

  logout() {
    this.authService.logout();
  }
}
