.container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: var(--background-color-dark);
  color: white;
  height: 5rem;
  box-sizing: border-box;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
  user-select: none;
}

.logo-container img {
  width: 3.5rem;
}

ul {
  display: flex;
  gap: 2rem;
}

ul li:hover {
  color: var(--accent-color);
}

.active{
  color: var(--accent-color);
}


