import {Component, ViewChild} from '@angular/core';
import {TagsSearchBarComponent} from "../../Inputs/search-bar/tags-search-bar.component";
import {OptionsPanelComponent} from "../../options-panel/options-panel.component";

@Component({
  selector: 'app-filters-bar',
  standalone: true,
  imports: [
    TagsSearchBarComponent,
    OptionsPanelComponent
  ],
  templateUrl: './filters-bar.component.html',
  styleUrl: './filters-bar.component.css'
})
export class FiltersBarComponent {
  @ViewChild(TagsSearchBarComponent) tagsSearchBar: TagsSearchBarComponent | undefined;
}
