
.container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: var(--accent-color-dark);
  transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
  height: var(--options-bar-height);
  gap: 1rem;
}

.options {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex-grow: 1;
  gap: 1rem;
}

app-arrow-button {
  width: 2rem;
  height: 2rem;
}

.slide-up {
  transform: translateY(-150%);
}

.to-right{
  justify-content: end;
}
