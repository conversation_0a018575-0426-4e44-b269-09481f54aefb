import {Component, Input} from '@angular/core';
import {<PERSON><PERSON>uttonComponent} from "../Inputs/buttons/arrow-button/arrow-button.component";
import {NgIf} from "@angular/common";

@Component({
  selector: 'app-options-panel',
  standalone: true,
  imports: [
    ArrowButtonComponent,
    NgIf
  ],
  templateUrl: './options-panel.component.html',
  styleUrl: './options-panel.component.css'
})
export class OptionsPanelComponent {
  @Input() isHideable: boolean = false;

  toggleVisibility($event: MouseEvent) {
    const target = $event.target as HTMLElement;
    target.parentElement?.parentElement?.classList.toggle('slide-up');
    target.classList.toggle('arrow-down');
  }
}
