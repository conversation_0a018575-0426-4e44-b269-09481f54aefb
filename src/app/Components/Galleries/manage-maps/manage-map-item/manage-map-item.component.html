<div class="container">
  <div class="hud" [routerLink]="isLink?['/map', map.id]:null">
    <div class="header">
      <div class="title">
        <h2>{{ map.name }}</h2>
        <p>{{ map.dateCreated }}</p>
      </div>
      <app-check-box *ngIf="isSelectable" [(isChecked)]="map.isSelected" [isChecked]="map.isSelected"></app-check-box>
    </div>
    <div *ngIf="isDeletable" class="panel">
      <p></p>
      <div class="buttons">
        <app-delete-button *ngIf="isDeletable" (onClick)="deleteMap(map.id)"></app-delete-button>
      </div>
    </div>
  </div>
  <img [ngClass]="{hidden: !isLoaded}" (load)="onLoad()" [ngSrc]="map.url" alt="There must have been a map" fill>
</div>
