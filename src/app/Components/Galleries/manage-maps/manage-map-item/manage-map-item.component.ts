import {Component, EventEmitter, Input, Output} from '@angular/core';
import {<PERSON><PERSON><PERSON>, NgIf, NgOptimizedImage} from "@angular/common";
import {ManageMapItemDto} from "../../../../Dtos/manage-map-item.dto";
import {DeleteButtonComponent} from "../../../Inputs/buttons/delete-button/delete-button.component";
import {MapService} from "../../../../Services/map.service";
import {RouterLink} from "@angular/router";
import {CheckBoxComponent} from "../../../Inputs/check-box/check-box.component";
import {FormsModule} from "@angular/forms";
import {NotificationService} from "../../../../Services/notification.service";

@Component({
  selector: 'app-manage-map-item',
  standalone: true,
  imports: [
    NgOptimizedImage,
    DeleteButtonComponent,
    RouterLink,
    CheckBoxComponent,
    NgI<PERSON>,
    FormsModule,
    NgClass
  ],
  templateUrl: './manage-map-item.component.html',
  styleUrl: './manage-map-item.component.css'
})
export class ManageMapItemComponent {
  @Input() map!: ManageMapItemDto;
  @Output() onDelete: EventEmitter<number> = new EventEmitter<number>();
  @Input() isSelectable: boolean = false;
  @Input() isDeletable: boolean = true;
  @Input() isLink: boolean = true;
  isLoaded: boolean = false;

  constructor(private mapService: MapService, private notificationService: NotificationService) {

  }

  deleteMap(mapId: number){
    window.confirm('Are you sure you want to delete this map?') &&
    this.mapService.deleteMap(mapId).subscribe(() => {
      this.onDelete.emit(this.map.id)
      this.notificationService.showSuccess('Map deleted successfully');
    });
  }

  onLoad() {
    this.isLoaded = true;
  }
}
