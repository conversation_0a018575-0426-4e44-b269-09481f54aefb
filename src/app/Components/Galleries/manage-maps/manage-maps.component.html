<div infiniteScroll class="gallery"
     (scrolled)="updateItems()"
     [infiniteScrollDistance]="1"
     [infiniteScrollThrottle]="30">
  <app-manage-map-item
    [style.width.%]="90 / itemsInRow"
    [isLink]="canMapItemLink"
    [isSelectable]="canMapItemSelect"
    [isDeletable]="canMapItemDelete"
    *ngFor="let map of items" (onDelete)="removeItem($event)"
    [map]="map"></app-manage-map-item>
</div>
