import {Component, Input} from '@angular/core';

import {ManageMapItemComponent} from "./manage-map-item/manage-map-item.component";
import {CommonModule, NgIf, NgOptimizedImage} from "@angular/common";
import {NgxMasonryModule} from "ngx-masonry";
import {InfiniteScrollDirective} from "ngx-infinite-scroll";
import {GalleryComponent} from "../gallery.component";
import {ManageMapItemDto} from "../../../Dtos/manage-map-item.dto";

@Component({
    selector: 'app-manage-creator-maps',
    standalone: true,
    imports: [
        ManageMapItemComponent,
        NgxMasonryModule,
        CommonModule,
        InfiniteScrollDirective,
        NgIf,
        NgOptimizedImage
    ],
    templateUrl: './manage-maps.component.html',
    styleUrl: './manage-maps.component.css'
})
export abstract class ManageMapsComponent extends GalleryComponent {
    override items: ManageMapItemDto[] = [];
    @Input() canMapItemSelect: boolean = false;
    @Input() canMapItemLink: boolean = true;
    @Input() canMapItemDelete: boolean = true;
    @Input() itemsInRow: number = 4;
}
