import {Component} from '@angular/core';

import {Observable} from "rxjs";
import {NgForOf} from "@angular/common";
import {NgxMasonryModule} from "ngx-masonry";
import {InfiniteScrollDirective} from "ngx-infinite-scroll";
import {ManageMapsComponent} from "../manage-maps.component";
import {ManageMapItemDto} from "../../../../Dtos/manage-map-item.dto";
import {ManageMapItemComponent} from "../manage-map-item/manage-map-item.component";
import {AuthService} from "../../../../Services/auth.service";
import {MapService} from "../../../../Services/map.service";


@Component({
    selector: 'app-manage-creator-maps',
    standalone: true,
    imports: [
        ManageMapItemComponent,
        NgForOf,
        NgxMasonryModule,
        InfiniteScrollDirective,
    ],
    templateUrl: '../manage-maps.component.html',
    styleUrl: '../manage-maps.component.css'
})
export class ManageCreatorMapsComponent extends ManageMapsComponent {
  constructor(protected authService: AuthService, mapService: MapService) {
    super(mapService);
  }
    override fetchItems(): Observable<ManageMapItemDto[]> {
      return this.mapService.getPreviewMapsByCreator(<number> this.authService.currentUserValue?.id, this.page);
    }
}
