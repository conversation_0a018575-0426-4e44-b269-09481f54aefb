import {Component, Input} from '@angular/core';

import {Observable} from "rxjs";
import {CommonModule} from "@angular/common";
import {NgxMasonryModule} from "ngx-masonry";
import {InfiniteScrollDirective} from "ngx-infinite-scroll";
import {ManageMapsComponent} from "../manage-maps.component";
import {ManageMapItemDto} from "../../../../Dtos/manage-map-item.dto";
import {ManageMapItemComponent} from "../manage-map-item/manage-map-item.component";
import {AuthService} from "../../../../Services/auth.service";
import {MapService} from "../../../../Services/map.service";


@Component({
  selector: 'app-manage-bundled-maps',
  standalone: true,
  imports: [
    ManageMapItemComponent,
    NgxMasonryModule,
    InfiniteScrollDirective,
    ManageMapItemComponent,
    NgxMasonryModule,
    CommonModule,
    InfiniteScrollDirective
  ],
  templateUrl: '../manage-maps.component.html',
  styleUrl: '../manage-maps.component.css'
})
export class ManageBundledMapsComponent extends ManageMapsComponent {
  @Input() mapId!: number;

  constructor(protected authService: AuthService, mapService: MapService) {
    super(mapService);
  }

  override fetchItems(): Observable<ManageMapItemDto[]> {
    return this.mapService.getPreviewBundledMaps(this.mapId, this.page);
  }
}
