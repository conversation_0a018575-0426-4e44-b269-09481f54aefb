import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {ManageBundleItemDto} from "../../../../Dtos/manage-bundle-item.dto";
import {DeleteButtonComponent} from "../../../Inputs/buttons/delete-button/delete-button.component";
import {Ng<PERSON>lass, NgForOf, NgOptimizedImage} from "@angular/common";
import {MapService} from "../../../../Services/map.service";
import {BundleService} from "../../../../Services/bundle.service";
import {NotificationService} from "../../../../Services/notification.service";

@Component({
  selector: 'app-manage-bundle-item',
  standalone: true,
  imports: [
    DeleteButtonComponent,
    NgOptimizedImage,
    NgForOf,
    NgClass
  ],
  templateUrl: './manage-bundle-item.component.html',
  styleUrl: './manage-bundle-item.component.css'
})
export class ManageBundleItemComponent implements OnInit {
  @Output() onDelete = new EventEmitter<number>();
  @Input() bundle!: ManageBundleItemDto;
  isLoaded: boolean = false;

  constructor(public mapService: MapService, public bundleService: BundleService, private notificationService: NotificationService) {
  }

  async ngOnInit(): Promise<void> {
    const promises = this.bundle.coverMapsUrls.map((url) => this.mapService.getMapPreview(url));
    const results = await Promise.allSettled(promises)
    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        this.bundle.coverMapsUrls[index] = result.value;
      }
    });
  }

  deleteBundle(id: number) {
    window.confirm('Are you sure you want to delete this bundle?') &&
    this.bundleService.deleteBundle(id).subscribe(() => this.onDelete.emit(this.bundle.id));
    this.notificationService.showSuccess('Bundle deleted successfully');
  }

  onLoad() {
    this.isLoaded = true;
  }
}
