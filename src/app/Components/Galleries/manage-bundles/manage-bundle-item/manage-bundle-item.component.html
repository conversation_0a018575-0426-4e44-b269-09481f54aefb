<div class="container">
  <div class="hud">
    <div class="header">
      <h2>{{ bundle.name }}</h2>
      <p>{{ bundle.dateCreated }}</p>
    </div>
    <div class="panel">
      <p></p>
      <div class="buttons">
        <app-delete-button (onClick)="deleteBundle(bundle.id)"></app-delete-button>
      </div>
    </div>
  </div>
  <div class="image-container">
    <img [ngClass]="{hidden: !isLoaded}"  (load)="onLoad()" *ngFor="let url of bundle.coverMapsUrls" [ngSrc]="url" alt="There must have been a map" fill>
  </div>
</div>
