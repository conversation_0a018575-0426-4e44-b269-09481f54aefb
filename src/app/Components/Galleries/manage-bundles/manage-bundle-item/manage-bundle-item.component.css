:host {
  position: relative;
  border-radius: 20px;
}

.container{
  box-sizing: border-box;
  position: absolute;
  z-index: 3;
  border-radius: 20px;
  overflow: hidden;
  border: 2px solid var(--background-color-dark);
  width: 100%;
  height: 100%;
}

.image-container{
  height: 100%;
  width: 100%;
  position: absolute;
  z-index: 1;
  object-fit: cover;
}
.image-container img{
  object-fit: cover;
  border-radius: 20px;
  border: 2px solid var(--background-color-dark);
  transition: opacity 1s;
}

.image-container > * {
  transform: translateY(calc(1rem * (var(--index))));
}

.image-container > *:nth-child(1) {
  --index: 0;
}

.image-container > *:nth-child(2) {
  --index: 1;
}

.image-container > *:nth-child(3) {
  --index: 2;
}

.image-container > *:nth-child(4) {
  --index: 3;
}

.image-container > *:nth-child(5) {
  --index: 4;
}

.image-container:hover{

}


.hud{
  position: absolute;
  z-index: 2;

  display: flex;
  justify-content: space-between;
  flex-direction: column;
  opacity: 0;
  transition: opacity 0.3s;
  height: 100%;
  width: 100%;
  backdrop-filter: blur(3px);
}

.hud:hover{
  opacity: 1;
  cursor: pointer;
  display: flex;
}

.header{
  box-sizing: border-box;
  width: 100%;
  padding: 1rem;
}

.header h2{
  font-size: 1.5rem;
  font-family: 'Roboto', sans-serif;
  color: var(--accent-color);
  text-shadow: var(--text-shadow);
}

.header p{
  font-size: 1.1rem;
  font-family: 'Roboto', sans-serif;
  color: var(--white);
  text-shadow: var(--text-shadow);
}

.panel{
  box-sizing: border-box;
  width: 100%;
  height: 25%;
  background-color: var(--background-color-dark);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
}

.panel p{
  font-size: 1.4rem;
  font-family: 'Roboto', sans-serif;
  color: var(--white);
  text-shadow: var(--text-shadow);
}

.buttons{
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 100%;
  min-width: 20%;
}

app-delete-button{
  aspect-ratio: 1/1;
  height: 80%;
}

.hidden{
  opacity: 0;
}
