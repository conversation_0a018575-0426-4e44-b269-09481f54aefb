import {Component} from '@angular/core';
import {GalleryComponent} from "../gallery.component";
import {AuthService} from "../../../Services/auth.service";
import {MapService} from "../../../Services/map.service";
import {ManageBundleItemDto} from "../../../Dtos/manage-bundle-item.dto";
import {BundleService} from "../../../Services/bundle.service";
import {Observable} from "rxjs";
import {InfiniteScrollDirective} from "ngx-infinite-scroll";
import {NgForOf} from "@angular/common";
import {ManageBundleItemComponent} from "./manage-bundle-item/manage-bundle-item.component";

@Component({
  selector: 'app-manage-bundles',
  standalone: true,
  imports: [
    InfiniteScrollDirective,
    NgForOf,
    ManageBundleItemComponent
  ],
  templateUrl: './manage-bundles.component.html',
  styleUrl: './manage-bundles.component.css'
})
export class ManageBundlesComponent extends GalleryComponent {
  override items: ManageBundleItemDto[] = [];

  constructor(private authService: AuthService, mapService: MapService, private bundleService: BundleService) {
    super(mapService);
  }

  override fetchItems(): Observable<ManageBundleItemDto[]> {
    return this.bundleService.getBundlesByCreator(<number>this.authService.currentUserValue?.id, this.page);
  }

  removeBundle(id: number) {
    this.items = this.items.filter(map => map.id !== id);
  }
}
