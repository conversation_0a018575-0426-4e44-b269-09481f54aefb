import {Component, OnInit} from '@angular/core';
import {MapService} from "../../Services/map.service";
import {Observable} from "rxjs";

@Component({
  selector: 'app-Galleries',
  standalone: true,
  styles: ``,
  template: ``,
})
export abstract class GalleryComponent implements OnInit {
  items: any[] = [];
  private itemsLoading: boolean = false;
  protected page: number = 0;

  protected constructor(protected mapService: MapService) {
  }

  ngOnInit(): void {
    this.updateItems();
  }

  updateItems(): void {
    if (this.itemsLoading)
      return;
    this.itemsLoading = true;
    this.fetchItems().subscribe((items: any[]) => {
      this.items = this.items.concat(items);
      this.itemsLoading = false;
      this.page++;
    });
  }
  abstract fetchItems(): Observable<any[]>;

  resetGallery(): void {
    this.items = [];
    this.page = 0;
    this.updateItems();
  }

  removeItem(id: number): void{
    this.items = this.items.filter(map => map.id !== id);
  }
}

