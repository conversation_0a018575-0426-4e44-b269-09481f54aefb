import {Component} from '@angular/core';
import {NgForOf, NgOptimizedImage} from "@angular/common";
import {NgxMasonryModule} from "ngx-masonry";
import {InfiniteScrollDirective} from "ngx-infinite-scroll";
import {Observable} from "rxjs";
import {MapsGalleryMasonryComponent} from "../maps-gallery-masonry.component";
import {MapService} from "../../../../Services/map.service";
import {MapDto} from "../../../../Dtos/map.dto";
import {MapCardComponent} from "../map-card/map-card.component";
import {FiltersBarComponent} from "../../../Header/filters-bar/filters-bar.component";

@Component({
  selector: 'app-Galleries',
  standalone: true,
  imports: [
    FiltersBarComponent,
    NgForOf,
    MapCardComponent,
    NgxMasonryModule,
    InfiniteScrollDirective,
    NgOptimizedImage,
  ],
  templateUrl: '../maps-gallery-masonry.component.html',
  styleUrl: '../maps-gallery-masonry.component.css'
})
export class FavoriteMapsGalleryMasonryComponent extends MapsGalleryMasonryComponent {
  constructor(mapService: MapService) {
    super(mapService);
  }

  override fetchItems(): Observable<MapDto[]> {
    return this.mapService.getFavoritePreviewMaps(this.page, this.tags);
  }
}

