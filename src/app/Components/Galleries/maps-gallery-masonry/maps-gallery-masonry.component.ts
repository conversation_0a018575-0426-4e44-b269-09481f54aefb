import {AfterViewInit, Component, ViewChild} from '@angular/core';
import {FiltersBarComponent} from "../../Header/filters-bar/filters-bar.component";
import {NgForOf, NgOptimizedImage} from "@angular/common";
import {MapCardComponent} from "./map-card/map-card.component";
import {NgxMasonryComponent, NgxMasonryModule, NgxMasonryOptions} from "ngx-masonry";
import {InfiniteScrollDirective} from "ngx-infinite-scroll";
import {MapService} from "../../../Services/map.service";
import {GalleryComponent} from "../gallery.component";
import {MapDto} from "../../../Dtos/map.dto";
import {Observable} from "rxjs";
import {TagDto} from "../../../Dtos/tag.dto";

@Component({
  selector: 'app-Galleries',
  standalone: true,
  imports: [
    FiltersBarComponent,
    NgForOf,
    MapCardComponent,
    NgxMasonryModule,
    InfiniteScrollDirective,
    NgOptimizedImage,
  ],
  templateUrl: './maps-gallery-masonry.component.html',
  styleUrl: './maps-gallery-masonry.component.css'
})
export class MapsGalleryMasonryComponent extends GalleryComponent implements AfterViewInit{
  @ViewChild(NgxMasonryComponent) masonry: NgxMasonryComponent | undefined;
  @ViewChild(FiltersBarComponent) filtersBar: FiltersBarComponent | undefined;

  override items: MapDto[] = [];

  public masonryOptions: NgxMasonryOptions = {
    columnWidth: '.map-card-container',
    gutter: '.gutter-sizer',
    percentPosition: true,
  };
  protected tags: TagDto[] = [];

  constructor(mapService: MapService) {
    super(mapService);
  }

  reloadMasonryLayout() {
    if (this.masonry !== undefined) {
      this.masonry.layout();
    }
  }

  onImageLoad() {
    this.reloadMasonryLayout();
  }

  fetchItems(): Observable<MapDto[]> {
    return this.mapService.getPreviewMaps(this.page, this.tags);
  }

  ngAfterViewInit() {
    this.filtersBar!.tagsSearchBar!.tagsChangedEventEmitter.subscribe(tags => {
      this.tags = tags;
      this.resetGallery();
    });
  }

}

