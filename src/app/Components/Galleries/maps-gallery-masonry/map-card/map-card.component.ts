import {Component, EventEmitter, Input, Output} from '@angular/core';
import {RouterLink} from "@angular/router";
import {NgIf} from "@angular/common";
import {FavoriteTogglerComponent} from "../../../Inputs/buttons/favorite-toggler/favorite-toggler.component";
import {AuthService} from "../../../../Services/auth.service";
import {IsMapFavorite} from "../../../../Dtos/is-map-favorite";

@Component({
  selector: 'app-map-card',
  standalone: true,
  imports: [
    RouterLink,
    NgIf,
    FavoriteTogglerComponent
  ],
  templateUrl: './map-card.component.html',
  styleUrl: './map-card.component.css'
})
export class MapCardComponent {
  @Input() map!: IsMapFavorite;
  @Output() onLoad: EventEmitter<void> = new EventEmitter<void>();
  isLoaded: boolean = false;

  constructor(protected authService: AuthService) {
  }

  load() {
    this.onLoad.emit();
    this.isLoaded = true;
  }
}
