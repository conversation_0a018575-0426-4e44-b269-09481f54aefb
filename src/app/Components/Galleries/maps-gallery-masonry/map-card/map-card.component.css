.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--background-color-dark);
  border-radius: 20px;
  height: fit-content;
  overflow: hidden;
  opacity: 1;
  transition: opacity 2s;
}

.container img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

app-favorite-toggler{
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
}

.loading{
  opacity: 0;
}
