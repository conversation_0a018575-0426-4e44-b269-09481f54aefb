app-map-card {
  margin-bottom: 7px;
  width: 100%;
}

.map-card-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 24%;
}

ngx-masonry {
  padding: 0.5%;
}

.container {
  display: block;
  width: 100%;
}

.grid-sizer {
  width: 24%;
}

.gutter-sizer {
  width: 1%;
}

app-filters-bar {
  position: sticky;
  left: 0;
  top: var(--navbar-height);
  width: 100%;
  z-index: 90;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 100px;
}
