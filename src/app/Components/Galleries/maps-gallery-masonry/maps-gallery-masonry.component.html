<app-filters-bar></app-filters-bar>
<div class="container">
  <ngx-masonry (scrolled)="updateItems()"
               [infiniteScrollDistance]="1"
               [infiniteScrollThrottle]="30"
               [options]="masonryOptions"
               [ordered]="true"
               infiniteScroll>
    <div class="gutter-sizer"></div>
    <div *ngFor="let map of items" class="map-card-container" ngxMasonryItem>
      <app-map-card (onLoad)="onImageLoad()" [map]="map"></app-map-card>
    </div>
  </ngx-masonry>
  <div class="loading-container">
    <img alt="Loading..." height="100" ngSrc="loading.gif" width="100">
  </div>
</div>
