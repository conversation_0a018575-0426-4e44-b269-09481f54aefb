import {ApplicationConfig, provideZoneChangeDetection} from '@angular/core';
import {provideRouter} from '@angular/router';

import {routes} from './app.routes';
import {HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi} from "@angular/common/http";
import {provideAnimations} from "@angular/platform-browser/animations";
import {MAT_DIALOG_DEFAULT_OPTIONS} from "@angular/material/dialog";
import {SessionInterceptor} from "./Config/session.interceptor";
import {initializeApp, provideFirebaseApp} from "@angular/fire/app";
import {getStorage, provideStorage} from "@angular/fire/storage";
import {firebaseConfig} from "./Config/firebase.config";
import {getAuth, provideAuth} from "@angular/fire/auth";
import {HttpErrorInterceptor} from "./Config/http-error.interceptor";


export const baseUrl = 'https://localhost/api';
export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({eventCoalescing: true}),
    provideRouter(routes),
    provideHttpClient(withInterceptorsFromDi()),
    {
      provide: HTTP_INTERCEPTORS,
      useClass: SessionInterceptor,
      multi: true
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: HttpErrorInterceptor,
      multi: true
    },
    provideAnimations(),
    {provide: MAT_DIALOG_DEFAULT_OPTIONS, useValue: {hasBackdrop: true}},
    provideFirebaseApp(() => initializeApp(firebaseConfig)),
    provideStorage(() => getStorage()),
    provideAuth(() => getAuth())

  ]
};
export const patterns = {
  name: {
    pattern: /^[a-zA-Z0-9 ]+$/,
    message: 'Name must contain only letters, numbers and spaces'
  },
  username: {
    pattern: /^[a-zA-Z0-9_]{4,20}$/,
    message: 'Username must contain only letters, numbers and underscores and be between 4 and 20 characters'
  },
  password: {
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/,
    message: 'Password must contain at least one uppercase letter, one lowercase letter and one number and be at least 8 characters long'
  }
};
export const constants = {
  maxMapWidth: 500,
  maxMapHeight: 500,
  maxSquareSideLength: 100,
  maxDescriptionLength: 1000,
  maxNameLength: 50,
}
