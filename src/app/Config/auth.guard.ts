import {Injectable} from '@angular/core';
import {ActivatedRouteSnapshot, CanActivate, Router} from '@angular/router';
import {AuthService} from "../Services/auth.service";
import {Role} from "../Dtos/role";
import {NotificationService} from "../Services/notification.service";


@Injectable({providedIn: 'root'})
export class AuthGuard implements CanActivate {

  constructor(private authService: AuthService, private router: Router, private notificationService: NotificationService) {
  }

  canActivate(route: ActivatedRouteSnapshot): boolean {
    let roles = route.data['roles'] as Role[];
    this.authService.updateCurrentUser();
    let currentRole = this.authService.currentUserValue?.role;

    for (let role of roles)
      if (role === currentRole)
        return true;
    this.router.navigate(['/maps']);
    this.notificationService.showError('Page is unavailable');
    return false;
  }
}
