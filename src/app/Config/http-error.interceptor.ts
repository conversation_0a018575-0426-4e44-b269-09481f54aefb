import {Injectable} from '@angular/core';
import {HttpErrorResponse, HttpEvent, HttpHandler, HttpInterceptor, HttpRequest} from '@angular/common/http';
import {Observable, throwError} from 'rxjs';
import {catchError} from 'rxjs/operators';
import {GlobalErrorService} from "../Services/global-error.service";

@Injectable()
export class HttpErrorInterceptor implements HttpInterceptor {

  constructor(private globalErrorService: GlobalErrorService) {
  }

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    return next.handle(req).pipe(
      catchError((error: HttpErrorResponse) => {
        this.globalErrorService.handleError(error);
        return throwError(() => new Error(error.message));
      })
    );
  }
}
