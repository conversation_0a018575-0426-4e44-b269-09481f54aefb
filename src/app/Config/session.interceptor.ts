import {Injectable} from '@angular/core';
import {<PERSON>ttpEvent, <PERSON>ttpHandler, HttpInterceptor, HttpRequest} from '@angular/common/http';
import {Observable} from 'rxjs';
import {AuthService} from "../Services/auth.service";

@Injectable()
export class SessionInterceptor implements HttpInterceptor {
  constructor(private authService: AuthService) {
  }

  intercept(request: HttpRequest<any>, next: <PERSON>ttpHandler): Observable<HttpEvent<any>> {
    const currentUser = this.authService.currentUserValue;
    if (currentUser && currentUser.token) {
      request = request.clone({
        setHeaders: {
          'X-Auth-Token': `${currentUser.token}`
        }
      });
    }
    return next.handle(request);
  }
}
