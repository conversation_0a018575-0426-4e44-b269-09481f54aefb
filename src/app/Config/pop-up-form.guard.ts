import {Injectable} from '@angular/core';
import {ActivatedRouteSnapshot, CanActivate} from '@angular/router';
import {MatDialog} from "@angular/material/dialog";


@Injectable({ providedIn: 'root' })
export class PopUpFormGuard implements CanActivate {
  constructor(private dialog: MatDialog) {}

  canActivate(route: ActivatedRouteSnapshot): boolean {
    const dialogComponent = route.data['dialogComponent'];
    const width = route.data['width'];

    if (!dialogComponent) {
      throw new Error('Dialog component not provided');
    }

    const dialog = this.dialog.open(dialogComponent, {
      width: width? width : '50vw',
      maxWidth: '90vw',

      backdropClass: 'custom-backdrop',
      disableClose: true,
    });

    dialog.backdropClick().subscribe(() => {
      if (confirm('Discard changes?')) dialog.close();
    });

    return false;
  }
}
