import {Injectable} from '@angular/core';
import {CanActivate} from '@angular/router';
import {MatDialog} from "@angular/material/dialog";
import {AuthenticationComponent} from "../Components/authentication/authentication.component";


@Injectable({providedIn: 'root'})
export class LoginGuard implements CanActivate {

  constructor(private dialog: MatDialog) {
  }

  canActivate(): boolean {
    this.dialog.open(AuthenticationComponent, {
      width: '500px',
      backdropClass: 'custom-backdrop',
      data: {method: 'login'}
    });
    return false;
  }
}
