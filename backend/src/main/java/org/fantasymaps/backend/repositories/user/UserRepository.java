package org.fantasymaps.backend.repositories.user;

import jakarta.validation.constraints.Email;
import org.fantasymaps.backend.config.AppConfig;
import org.fantasymaps.backend.model.user.User;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

public interface UserRepository extends JpaRepository<User, Integer> {

    Optional<User> findByUsernameAndPassword(String username, String password);

    Optional<User> findByUsername(String username);

    Optional<User> findByActivationToken(String activationToken);

    Optional<User> findByEmail(@Email(message = AppConfig.emailMismatchMessage) String email);
}