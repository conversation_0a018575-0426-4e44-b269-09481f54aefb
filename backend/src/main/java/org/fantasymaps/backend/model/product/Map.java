package org.fantasymaps.backend.model.product;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.fantasymaps.backend.config.AppConfig;
import org.fantasymaps.backend.model.Tag;

import java.util.LinkedHashSet;
import java.util.Set;

@Getter
@Setter
@Entity
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class Map extends Product {
    @Column(name = "name", length = AppConfig.nameMaxLength)
    @NotBlank(message = "Name cannot be blank")
    @Pattern(regexp = AppConfig.namePattern, message = AppConfig.nameMismatchMessage)
    private String name;
    @Embedded
    private MapSize size;
    @Column(name = "description", length = AppConfig.descriptionMaxLength)
    private String description;
    @Column(name = "map_image_url")
    @NotBlank(message = "Map image URL cannot be blank")
    private String url;
    @ManyToMany(cascade = CascadeType.PERSIST)
    @JoinTable(name = "map_tags",
            joinColumns = @JoinColumn(name = "map_id"),
            inverseJoinColumns = @JoinColumn(name = "tags_id"))
    private Set<Tag> tags = new LinkedHashSet<>();
    @ManyToMany(mappedBy = "maps")
    private Set<Bundle> bundles = new LinkedHashSet<>();
}