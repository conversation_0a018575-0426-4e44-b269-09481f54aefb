package org.fantasymaps.backend.model.user;

import jakarta.persistence.*;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.fantasymaps.backend.config.AppConfig;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDate;

@Getter
@Setter
@Entity
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "app_user")
public abstract class User {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "id")
    private Integer id;
    @Column(name = "username", length = AppConfig.usernameMaxLength)
    @NotBlank(message = "Username is mandatory")
    @Pattern(
            regexp = AppConfig.usernamePattern,
            message = AppConfig.usernameMismatchMessage
    )
    private String username;
    @Column(name = "password")
    @NotBlank(message = "Password is mandatory")
    @Pattern(
            regexp = AppConfig.passwordPattern,
            message = AppConfig.passwordMismatchMessage
    )
    private String password;
    @Column(name = "email", unique = true)
    @Email(message = AppConfig.emailMismatchMessage)
    private String email;
    @Column(name = "date_created")
    @CreationTimestamp
    private LocalDate createdDate;
    @Column(name = "activation_token", unique = true)
    private String activationToken;
}

