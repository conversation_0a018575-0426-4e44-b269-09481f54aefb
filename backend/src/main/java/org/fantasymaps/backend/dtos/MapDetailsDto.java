package org.fantasymaps.backend.dtos;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Set;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class MapDetailsDto {
    private int id;
    @NotBlank(message = "Name cannot be blank")
    private String name;
    @NotBlank(message = "Map image URL cannot be blank")
    private String url;
    private Boolean isFavorite;
    private Set<TagDto> tags;
    private String description;
    private MapSizeDto size;
}