version: '3.8'
services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "4200:4200"
    networks:
      - mynetwork

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    depends_on:
      - postgres
    networks:
      - mynetwork

  redis:
    image: redis:latest
    ports:
      - "6379:6379"
    networks:
      - mynetwork

  postgres:
    image: postgres:latest
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: pjatk
      POSTGRES_DB: fantasy-maps
      POSTGRES_HOST_AUTH_METHOD: scram-sha-256
      POSTGRES_INITDB_ARGS: --auth-host=scram-sha-256
    ports:
      - "5432:5432"
    networks:
      - mynetwork

networks:
  mynetwork:
    driver: bridge
