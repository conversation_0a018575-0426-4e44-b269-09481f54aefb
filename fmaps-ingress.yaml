apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: app-ingress
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/backend-protocol: "HTTPS"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
      - localhost
    secretName: app-ingress-secret
  rules:
    - host: localhost
      http:
        paths:
          - path: /api
            pathType: Prefix
            backend:
              service:
                name: spring-backend
                port:
                  number: 8443
          - path: /
            pathType: Prefix
            backend:
              service:
                name: angular-frontend
                port:
                  number: 80